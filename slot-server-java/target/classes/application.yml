# SLOT Server Application Configuration
# Replaces C++ configuration files and hardcoded settings

server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /slot-server
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

spring:
  application:
    name: slot-server
  
  # Database Configuration
  # Replaces C++ ODBC connection settings
  datasource:
    url: ${DB_URL:************************************************************************************************}
    username: ${DB_USERNAME:slot_user}
    password: ${DB_PASSWORD:slot_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: ${DB_MAX_POOL_SIZE:20}
      minimum-idle: ${DB_MIN_IDLE:5}
      idle-timeout: 300000
      max-lifetime: 1200000
      connection-timeout: 20000
      leak-detection-threshold: 60000
  
  # JPA Configuration
  # Replaces C++ manual SQL and ORM
  jpa:
    hibernate:
      ddl-auto: ${DDL_AUTO:validate}
      naming:
        physical-strategy: org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy
    show-sql: ${SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
  
  # Redis Configuration for Caching
  # Replaces C++ in-memory caching
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  # Cache Configuration
  cache:
    type: redis
    redis:
      time-to-live: 600000  # 10 minutes
      cache-null-values: false
  
  # Security Configuration
  security:
    user:
      name: ${ADMIN_USERNAME:admin}
      password: ${ADMIN_PASSWORD:admin123}
  
  # Jackson JSON Configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
      indent-output: true
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null
  
  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
      enabled: true

# Logging Configuration
# Replaces C++ logging system
logging:
  level:
    com.ssa.slot: ${LOG_LEVEL:INFO}
    org.springframework.web: ${WEB_LOG_LEVEL:WARN}
    org.hibernate.SQL: ${SQL_LOG_LEVEL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: ${SQL_PARAM_LOG_LEVEL:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE:logs/slot-server.log}
    max-size: 100MB
    max-history: 30

# Management and Monitoring
# Replaces C++ system monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# OpenAPI/Swagger Configuration
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  show-actuator: true

# Application Specific Configuration
# Replaces C++ application settings
slot:
  # Engine Integration Configuration
  engine:
    # Connection type: socket, jni, rest
    connection-type: ${ENGINE_CONNECTION_TYPE:socket}
    host: ${ENGINE_HOST:localhost}
    port: ${ENGINE_PORT:6010}
    timeout: ${ENGINE_TIMEOUT:300000}  # 5 minutes
    retry-attempts: ${ENGINE_RETRY:3}
    
  # Optimization Configuration
  optimization:
    max-concurrent-jobs: ${MAX_OPTIMIZATION_JOBS:5}
    job-timeout: ${OPTIMIZATION_TIMEOUT:3600000}  # 1 hour
    cleanup-interval: ${CLEANUP_INTERVAL:300000}  # 5 minutes
    
  # File Storage Configuration
  storage:
    cad-files-path: ${CAD_FILES_PATH:./storage/cad}
    reports-path: ${REPORTS_PATH:./storage/reports}
    temp-path: ${TEMP_PATH:./storage/temp}
    max-file-size: ${MAX_FILE_SIZE:100MB}
    
  # Import/Export Configuration
  import:
    batch-size: ${IMPORT_BATCH_SIZE:1000}
    max-errors: ${IMPORT_MAX_ERRORS:100}
    
  # Security Configuration
  security:
    jwt:
      secret: ${JWT_SECRET:slot-server-secret-key-change-in-production}
      expiration: ${JWT_EXPIRATION:86400000}  # 24 hours
    cors:
      allowed-origins: ${CORS_ORIGINS:http://localhost:3000,http://localhost:8080}
      allowed-methods: GET,POST,PUT,DELETE,PATCH,OPTIONS
      allowed-headers: "*"
      allow-credentials: true

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: jdbc:h2:mem:slot_dev;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  
  h2:
    console:
      enabled: true
      path: /h2-console

logging:
  level:
    com.ssa.slot: DEBUG
    org.hibernate.SQL: DEBUG

slot:
  engine:
    connection-type: mock  # Use mock engine for development

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

logging:
  level:
    com.ssa.slot: INFO
    org.springframework.web: WARN
  file:
    name: /var/log/slot-server/application.log

slot:
  security:
    jwt:
      secret: ${JWT_SECRET}  # Must be provided in production

# SLOT Server - Java Implementation

## 概述

这是SLOT仓储优化系统Server层的Java重写版本。该项目将原有的C++服务器端代码转换为现代化的Java Spring Boot应用，同时保留原有的C++优化引擎。

## 项目结构

```
slot-server-java/
├── src/main/java/com/ssa/slot/
│   ├── SlotServerApplication.java          # 应用程序入口
│   ├── entity/                             # 实体类（替代C++ SLOTObject类）
│   │   ├── BaseEntity.java                 # 基础实体类
│   │   ├── Facility.java                   # 设施实体
│   │   ├── ProductPack.java                # 产品包装实体
│   │   ├── Section.java                    # 区域实体
│   │   ├── Aisle.java                      # 通道实体
│   │   └── ...                             # 其他实体类
│   ├── repository/                         # 数据访问层（替代C++ DataView类）
│   │   ├── FacilityRepository.java         # 设施数据访问
│   │   ├── ProductPackRepository.java      # 产品数据访问
│   │   └── ...                             # 其他Repository
│   ├── service/                            # 业务服务层（替代C++ DataAdministrator类）
│   │   ├── FacilityService.java            # 设施业务服务
│   │   ├── OptimizationService.java        # 优化服务
│   │   └── ...                             # 其他服务类
│   ├── controller/                         # REST API控制器（新增）
│   │   ├── FacilityController.java         # 设施API
│   │   ├── OptimizationController.java     # 优化API
│   │   └── ...                             # 其他控制器
│   ├── dto/                                # 数据传输对象（新增）
│   │   ├── ApiResponse.java                # 统一API响应
│   │   ├── FacilityCreateRequest.java      # 设施创建请求
│   │   └── ...                             # 其他DTO
│   ├── mapper/                             # 对象映射（新增）
│   │   └── FacilityMapper.java             # 设施对象映射
│   ├── config/                             # 配置类（新增）
│   │   ├── DatabaseConfig.java             # 数据库配置
│   │   ├── SecurityConfig.java             # 安全配置
│   │   └── ...                             # 其他配置
│   └── exception/                          # 异常处理（新增）
│       ├── GlobalExceptionHandler.java     # 全局异常处理
│       └── ...                             # 自定义异常
├── src/main/resources/
│   ├── application.yml                     # 应用配置
│   ├── db/migration/                       # 数据库迁移脚本
│   └── ...
├── src/test/                               # 测试代码
├── pom.xml                                 # Maven配置
└── README.md                               # 项目说明
```

## 技术栈

### 核心框架
- **Spring Boot 3.2.0** - 应用框架
- **Spring Data JPA** - 数据访问层
- **Spring Security** - 安全框架
- **Spring Web** - Web层框架

### 数据库
- **MySQL 8.0** - 主数据库
- **Redis** - 缓存数据库
- **H2** - 测试数据库

### 工具库
- **Lombok** - 减少样板代码
- **MapStruct** - 对象映射
- **Jackson** - JSON处理
- **Swagger/OpenAPI** - API文档

### 测试框架
- **JUnit 5** - 单元测试
- **Testcontainers** - 集成测试
- **Spring Boot Test** - Spring测试支持

## 快速开始

### 环境要求
- Java 17+
- Maven 3.8+
- MySQL 8.0+
- Redis 6.0+ (可选)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd slot-server-java
```

2. **配置数据库**
```sql
-- 创建数据库
CREATE DATABASE slot_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'slot_user'@'localhost' IDENTIFIED BY 'slot_password';
GRANT ALL PRIVILEGES ON slot_db.* TO 'slot_user'@'localhost';
FLUSH PRIVILEGES;
```

3. **配置应用**
```bash
# 复制配置文件
cp src/main/resources/application.yml.example src/main/resources/application.yml

# 编辑配置文件，设置数据库连接等
vim src/main/resources/application.yml
```

4. **构建和运行**
```bash
# 构建项目
mvn clean compile

# 运行测试
mvn test

# 启动应用
mvn spring-boot:run
```

5. **访问应用**
- API文档: http://localhost:8080/slot-server/swagger-ui.html
- 健康检查: http://localhost:8080/slot-server/actuator/health

## 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    url: ***********************************
    username: slot_user
    password: slot_password
```

### Engine集成配置
```yaml
slot:
  engine:
    connection-type: socket  # socket, jni, rest
    host: localhost
    port: 6010
    timeout: 300000
```

### 缓存配置
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
```

## API文档

### 设施管理API

#### 创建设施
```http
POST /api/v1/facilities
Content-Type: application/json

{
  "name": "Distribution Center 1",
  "description": "Main distribution center",
  "facilityType": "Distribution Center",
  "dimensions": {
    "width": 100.0,
    "height": 20.0,
    "depth": 200.0
  }
}
```

#### 获取设施列表
```http
GET /api/v1/facilities?page=0&size=20
```

#### 获取设施详情
```http
GET /api/v1/facilities/{id}
```

### 优化API

#### 执行Pass1优化
```http
POST /api/v1/facilities/{facilityId}/optimization/pass1
Content-Type: application/json

{
  "options": {
    "regressionType": "LINEAR",
    "hotspotWeight": 0.8
  }
}
```

## 开发指南

### 添加新实体

1. **创建实体类**
```java
@Entity
@Table(name = "new_entity")
@Data
@EqualsAndHashCode(callSuper = true)
public class NewEntity extends BaseEntity {
    // 字段定义
}
```

2. **创建Repository**
```java
@Repository
public interface NewEntityRepository extends JpaRepository<NewEntity, Long> {
    // 自定义查询方法
}
```

3. **创建Service**
```java
@Service
@Transactional
public class NewEntityService {
    // 业务逻辑
}
```

4. **创建Controller**
```java
@RestController
@RequestMapping("/api/v1/new-entities")
public class NewEntityController {
    // REST API端点
}
```

### 数据库迁移

使用Flyway进行数据库版本管理：

```sql
-- src/main/resources/db/migration/V1__Create_facility_table.sql
CREATE TABLE slot_facility (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description VARCHAR(500),
    -- 其他字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 测试

#### 单元测试
```java
@ExtendWith(MockitoExtension.class)
class FacilityServiceTest {
    @Mock
    private FacilityRepository facilityRepository;
    
    @InjectMocks
    private FacilityService facilityService;
    
    @Test
    void shouldCreateFacility() {
        // 测试逻辑
    }
}
```

#### 集成测试
```java
@SpringBootTest
@Testcontainers
class FacilityControllerIntegrationTest {
    @Container
    static MySQLContainer<?> mysql = new MySQLContainer<>("mysql:8.0");
    
    @Test
    void shouldCreateFacilityViaAPI() {
        // 集成测试逻辑
    }
}
```

## 部署

### Docker部署

1. **构建镜像**
```bash
mvn spring-boot:build-image
```

2. **运行容器**
```bash
docker run -p 8080:8080 \
  -e DB_URL=********************************************** \
  -e DB_USERNAME=slot_user \
  -e DB_PASSWORD=slot_password \
  slot-server:latest
```

### Kubernetes部署

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: slot-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: slot-server
  template:
    metadata:
      labels:
        app: slot-server
    spec:
      containers:
      - name: slot-server
        image: slot-server:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_URL
          value: "***************************************"
```

## 监控和运维

### 健康检查
```bash
curl http://localhost:8080/slot-server/actuator/health
```

### 指标监控
```bash
curl http://localhost:8080/slot-server/actuator/metrics
```

### 日志查看
```bash
tail -f logs/slot-server.log
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接字符串和凭据
   - 确认防火墙设置

2. **Engine连接失败**
   - 检查C++引擎是否运行
   - 验证端口配置
   - 检查网络连接

3. **内存不足**
   - 调整JVM堆大小：`-Xmx2g`
   - 优化查询和缓存策略

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

[许可证信息]

## 联系方式

- 项目维护者: [维护者信息]
- 问题反馈: [Issue链接]
- 文档: [文档链接]

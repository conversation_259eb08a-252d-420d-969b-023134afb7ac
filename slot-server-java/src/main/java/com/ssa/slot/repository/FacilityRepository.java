package com.ssa.slot.repository;

import com.ssa.slot.entity.Facility;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Facility Repository
 * 
 * Replaces the C++ SLOTFacilityDV (Data View) class functionality.
 * Provides data access methods for Facility entities.
 * 
 * Original C++ equivalent: Server/SLOTFacilityDV.h/cpp
 * 
 * Key improvements over C++:
 * - Spring Data JPA automatic query generation
 * - Type-safe query methods
 * - Pagination and sorting support
 * - Specification support for complex queries
 */
@Repository
public interface FacilityRepository extends JpaRepository<Facility, Long>, JpaSpecificationExecutor<Facility> {

    /**
     * Find facility by name (case-insensitive)
     * Replaces C++ GetFacilityByName functionality
     */
    Optional<Facility> findByNameIgnoreCase(String name);

    /**
     * Find facilities by status
     * Replaces C++ filtered queries by status
     */
    List<Facility> findByStatus(Facility.FacilityStatus status);

    /**
     * Find facilities by type
     */
    List<Facility> findByFacilityType(String facilityType);

    /**
     * Find active facilities
     * Common query pattern from C++
     */
    @Query("SELECT f FROM Facility f WHERE f.status = 'ACTIVE' AND f.deleted = false")
    List<Facility> findActiveFacilities();

    /**
     * Find facilities with CAD files
     * Replaces C++ queries for facilities with drawing files
     */
    @Query("SELECT f FROM Facility f WHERE f.cadFilePath IS NOT NULL AND f.cadFilePath != ''")
    List<Facility> findFacilitiesWithCadFiles();

    /**
     * Find integrated facilities
     * Replaces C++ integration status queries
     */
    List<Facility> findByIntegratedTrue();

    /**
     * Find facilities by name containing (case-insensitive search)
     * Useful for autocomplete and search functionality
     */
    Page<Facility> findByNameContainingIgnoreCase(String name, Pageable pageable);

    /**
     * Count total locations in a facility
     * Replaces C++ aggregate queries for location counts
     */
    @Query("SELECT COUNT(l) FROM Facility f " +
           "JOIN f.sections s " +
           "JOIN s.aisles a " +
           "JOIN a.bays b " +
           "JOIN b.levels lv " +
           "JOIN lv.locations l " +
           "WHERE f.id = :facilityId AND l.deleted = false")
    long countLocationsByFacilityId(@Param("facilityId") Long facilityId);

    /**
     * Count total products in a facility
     * Replaces C++ product count queries
     */
    @Query("SELECT COUNT(p) FROM Facility f " +
           "JOIN f.products p " +
           "WHERE f.id = :facilityId AND p.deleted = false")
    long countProductsByFacilityId(@Param("facilityId") Long facilityId);

    /**
     * Find facilities with products having movement data
     * Useful for optimization readiness checks
     */
    @Query("SELECT DISTINCT f FROM Facility f " +
           "JOIN f.products p " +
           "WHERE p.movement > 0 AND p.deleted = false AND f.deleted = false")
    List<Facility> findFacilitiesWithMovementData();

    /**
     * Get facility summary information
     * Replaces C++ summary queries
     */
    @Query("SELECT f.id, f.name, f.status, " +
           "COUNT(DISTINCT s.id) as sectionCount, " +
           "COUNT(DISTINCT a.id) as aisleCount, " +
           "COUNT(DISTINCT b.id) as bayCount, " +
           "COUNT(DISTINCT l.id) as locationCount, " +
           "COUNT(DISTINCT p.id) as productCount " +
           "FROM Facility f " +
           "LEFT JOIN f.sections s " +
           "LEFT JOIN s.aisles a " +
           "LEFT JOIN a.bays b " +
           "LEFT JOIN b.levels lv " +
           "LEFT JOIN lv.locations l " +
           "LEFT JOIN f.products p " +
           "WHERE f.id = :facilityId AND f.deleted = false " +
           "GROUP BY f.id, f.name, f.status")
    Object[] getFacilitySummary(@Param("facilityId") Long facilityId);

    /**
     * Check if facility exists by name (excluding current facility)
     * Useful for validation during updates
     */
    @Query("SELECT CASE WHEN COUNT(f) > 0 THEN true ELSE false END " +
           "FROM Facility f " +
           "WHERE LOWER(f.name) = LOWER(:name) AND f.id != :excludeId AND f.deleted = false")
    boolean existsByNameIgnoreCaseAndIdNot(@Param("name") String name, @Param("excludeId") Long excludeId);

    /**
     * Find facilities ready for optimization
     * A facility is ready if it has products with movement data and locations
     */
    @Query("SELECT DISTINCT f FROM Facility f " +
           "WHERE f.status = 'ACTIVE' " +
           "AND f.deleted = false " +
           "AND EXISTS (SELECT 1 FROM f.products p WHERE p.movement > 0 AND p.deleted = false) " +
           "AND EXISTS (SELECT 1 FROM f.sections s " +
           "           JOIN s.aisles a " +
           "           JOIN a.bays b " +
           "           JOIN b.levels lv " +
           "           JOIN lv.locations l " +
           "           WHERE l.deleted = false)")
    List<Facility> findFacilitiesReadyForOptimization();

    /**
     * Get facilities with their product and location counts
     * Useful for dashboard and reporting
     */
    @Query("SELECT f, " +
           "COUNT(DISTINCT p.id) as productCount, " +
           "COUNT(DISTINCT l.id) as locationCount " +
           "FROM Facility f " +
           "LEFT JOIN f.products p " +
           "LEFT JOIN f.sections s " +
           "LEFT JOIN s.aisles a " +
           "LEFT JOIN a.bays b " +
           "LEFT JOIN b.levels lv " +
           "LEFT JOIN lv.locations l " +
           "WHERE f.deleted = false " +
           "GROUP BY f " +
           "ORDER BY f.name")
    List<Object[]> findFacilitiesWithCounts();

    /**
     * Find facilities by area range
     * Useful for filtering by facility size
     */
    @Query("SELECT f FROM Facility f " +
           "WHERE f.dimensions.area BETWEEN :minArea AND :maxArea " +
           "AND f.deleted = false")
    List<Facility> findByAreaBetween(@Param("minArea") Double minArea, @Param("maxArea") Double maxArea);

    /**
     * Custom method to find facilities with specific criteria
     * This would replace complex C++ query building logic
     */
    @Query("SELECT f FROM Facility f " +
           "WHERE (:name IS NULL OR LOWER(f.name) LIKE LOWER(CONCAT('%', :name, '%'))) " +
           "AND (:status IS NULL OR f.status = :status) " +
           "AND (:facilityType IS NULL OR f.facilityType = :facilityType) " +
           "AND (:integrated IS NULL OR f.integrated = :integrated) " +
           "AND f.deleted = false " +
           "ORDER BY f.name")
    Page<Facility> findFacilitiesByCriteria(
            @Param("name") String name,
            @Param("status") Facility.FacilityStatus status,
            @Param("facilityType") String facilityType,
            @Param("integrated") Boolean integrated,
            Pageable pageable);
}

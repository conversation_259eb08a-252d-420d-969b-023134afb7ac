package com.ssa.slot.controller;

import com.ssa.slot.dto.*;
import com.ssa.slot.entity.Facility;
import com.ssa.slot.service.FacilityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Facility REST Controller
 * 
 * Provides REST API endpoints for facility management.
 * Replaces the C++ server-side facility handling with modern REST API.
 * 
 * Original C++ equivalent: Server communication layer and command handlers
 * 
 * Key improvements over C++:
 * - RESTful API design
 * - OpenAPI/Swagger documentation
 * - Standardized HTTP status codes
 * - JSON request/response handling
 * - Validation and error handling
 */
@RestController
@RequestMapping("/api/v1/facilities")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Facility Management", description = "APIs for managing warehouse facilities")
public class FacilityController {

    private final FacilityService facilityService;

    /**
     * Create a new facility
     * POST /api/v1/facilities
     */
    @PostMapping
    @Operation(summary = "Create a new facility", description = "Creates a new warehouse facility")
    public ResponseEntity<ApiResponse<FacilityResponse>> createFacility(
            @Valid @RequestBody FacilityCreateRequest request) {
        
        log.info("Creating facility: {}", request.getName());
        
        FacilityResponse facility = facilityService.createFacility(request);
        
        ApiResponse<FacilityResponse> response = ApiResponse.<FacilityResponse>builder()
                .success(true)
                .message("Facility created successfully")
                .data(facility)
                .build();
        
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Get facility by ID
     * GET /api/v1/facilities/{id}
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get facility by ID", description = "Retrieves a facility by its unique identifier")
    public ResponseEntity<ApiResponse<FacilityResponse>> getFacility(
            @Parameter(description = "Facility ID") @PathVariable Long id) {
        
        log.debug("Getting facility: {}", id);
        
        FacilityResponse facility = facilityService.getFacility(id);
        
        ApiResponse<FacilityResponse> response = ApiResponse.<FacilityResponse>builder()
                .success(true)
                .message("Facility retrieved successfully")
                .data(facility)
                .build();
        
        return ResponseEntity.ok(response);
    }

    /**
     * Get all facilities with pagination
     * GET /api/v1/facilities
     */
    @GetMapping
    @Operation(summary = "Get all facilities", description = "Retrieves all facilities with pagination support")
    public ResponseEntity<ApiResponse<Page<FacilityResponse>>> getAllFacilities(
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("Getting all facilities with pagination");
        
        Page<FacilityResponse> facilities = facilityService.getAllFacilities(pageable);
        
        ApiResponse<Page<FacilityResponse>> response = ApiResponse.<Page<FacilityResponse>>builder()
                .success(true)
                .message("Facilities retrieved successfully")
                .data(facilities)
                .build();
        
        return ResponseEntity.ok(response);
    }

    /**
     * Get active facilities
     * GET /api/v1/facilities/active
     */
    @GetMapping("/active")
    @Operation(summary = "Get active facilities", description = "Retrieves all active facilities")
    public ResponseEntity<ApiResponse<List<FacilityResponse>>> getActiveFacilities() {
        
        log.debug("Getting active facilities");
        
        List<FacilityResponse> facilities = facilityService.getActiveFacilities();
        
        ApiResponse<List<FacilityResponse>> response = ApiResponse.<List<FacilityResponse>>builder()
                .success(true)
                .message("Active facilities retrieved successfully")
                .data(facilities)
                .build();
        
        return ResponseEntity.ok(response);
    }

    /**
     * Search facilities by criteria
     * GET /api/v1/facilities/search
     */
    @GetMapping("/search")
    @Operation(summary = "Search facilities", description = "Search facilities by various criteria")
    public ResponseEntity<ApiResponse<Page<FacilityResponse>>> searchFacilities(
            @Parameter(description = "Facility name (partial match)") @RequestParam(required = false) String name,
            @Parameter(description = "Facility status") @RequestParam(required = false) Facility.FacilityStatus status,
            @Parameter(description = "Facility type") @RequestParam(required = false) String facilityType,
            @Parameter(description = "Integration status") @RequestParam(required = false) Boolean integrated,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("Searching facilities with criteria");
        
        Page<FacilityResponse> facilities = facilityService.searchFacilities(
                name, status, facilityType, integrated, pageable);
        
        ApiResponse<Page<FacilityResponse>> response = ApiResponse.<Page<FacilityResponse>>builder()
                .success(true)
                .message("Facilities search completed successfully")
                .data(facilities)
                .build();
        
        return ResponseEntity.ok(response);
    }

    /**
     * Update facility
     * PUT /api/v1/facilities/{id}
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update facility", description = "Updates an existing facility")
    public ResponseEntity<ApiResponse<FacilityResponse>> updateFacility(
            @Parameter(description = "Facility ID") @PathVariable Long id,
            @Valid @RequestBody FacilityUpdateRequest request) {
        
        log.info("Updating facility: {}", id);
        
        FacilityResponse facility = facilityService.updateFacility(id, request);
        
        ApiResponse<FacilityResponse> response = ApiResponse.<FacilityResponse>builder()
                .success(true)
                .message("Facility updated successfully")
                .data(facility)
                .build();
        
        return ResponseEntity.ok(response);
    }

    /**
     * Get facility summary
     * GET /api/v1/facilities/{id}/summary
     */
    @GetMapping("/{id}/summary")
    @Operation(summary = "Get facility summary", description = "Retrieves facility summary with statistics")
    public ResponseEntity<ApiResponse<FacilitySummaryResponse>> getFacilitySummary(
            @Parameter(description = "Facility ID") @PathVariable Long id) {
        
        log.debug("Getting facility summary: {}", id);
        
        FacilitySummaryResponse summary = facilityService.getFacilitySummary(id);
        
        ApiResponse<FacilitySummaryResponse> response = ApiResponse.<FacilitySummaryResponse>builder()
                .success(true)
                .message("Facility summary retrieved successfully")
                .data(summary)
                .build();
        
        return ResponseEntity.ok(response);
    }

    /**
     * Check optimization readiness
     * GET /api/v1/facilities/{id}/optimization-ready
     */
    @GetMapping("/{id}/optimization-ready")
    @Operation(summary = "Check optimization readiness", description = "Checks if facility is ready for optimization")
    public ResponseEntity<ApiResponse<Boolean>> checkOptimizationReadiness(
            @Parameter(description = "Facility ID") @PathVariable Long id) {
        
        log.debug("Checking optimization readiness for facility: {}", id);
        
        boolean ready = facilityService.isFacilityReadyForOptimization(id);
        
        ApiResponse<Boolean> response = ApiResponse.<Boolean>builder()
                .success(true)
                .message("Optimization readiness checked successfully")
                .data(ready)
                .build();
        
        return ResponseEntity.ok(response);
    }

    /**
     * Get facilities ready for optimization
     * GET /api/v1/facilities/optimization-ready
     */
    @GetMapping("/optimization-ready")
    @Operation(summary = "Get optimization-ready facilities", description = "Retrieves all facilities ready for optimization")
    public ResponseEntity<ApiResponse<List<FacilityResponse>>> getOptimizationReadyFacilities() {
        
        log.debug("Getting optimization-ready facilities");
        
        List<FacilityResponse> facilities = facilityService.getFacilitiesReadyForOptimization();
        
        ApiResponse<List<FacilityResponse>> response = ApiResponse.<List<FacilityResponse>>builder()
                .success(true)
                .message("Optimization-ready facilities retrieved successfully")
                .data(facilities)
                .build();
        
        return ResponseEntity.ok(response);
    }

    /**
     * Update CAD file path
     * PATCH /api/v1/facilities/{id}/cad-file
     */
    @PatchMapping("/{id}/cad-file")
    @Operation(summary = "Update CAD file path", description = "Updates the CAD file path for a facility")
    public ResponseEntity<ApiResponse<Void>> updateCadFilePath(
            @Parameter(description = "Facility ID") @PathVariable Long id,
            @RequestBody CadFileUpdateRequest request) {
        
        log.info("Updating CAD file path for facility: {}", id);
        
        facilityService.updateCadFilePath(id, request.getCadFilePath());
        
        ApiResponse<Void> response = ApiResponse.<Void>builder()
                .success(true)
                .message("CAD file path updated successfully")
                .build();
        
        return ResponseEntity.ok(response);
    }

    /**
     * Update integration status
     * PATCH /api/v1/facilities/{id}/integration-status
     */
    @PatchMapping("/{id}/integration-status")
    @Operation(summary = "Update integration status", description = "Updates the integration status of a facility")
    public ResponseEntity<ApiResponse<Void>> updateIntegrationStatus(
            @Parameter(description = "Facility ID") @PathVariable Long id,
            @RequestBody IntegrationStatusUpdateRequest request) {
        
        log.info("Updating integration status for facility: {}", id);
        
        facilityService.updateIntegrationStatus(id, request.getIntegrated());
        
        ApiResponse<Void> response = ApiResponse.<Void>builder()
                .success(true)
                .message("Integration status updated successfully")
                .build();
        
        return ResponseEntity.ok(response);
    }

    /**
     * Delete facility (soft delete)
     * DELETE /api/v1/facilities/{id}
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete facility", description = "Soft deletes a facility")
    public ResponseEntity<ApiResponse<Void>> deleteFacility(
            @Parameter(description = "Facility ID") @PathVariable Long id) {
        
        log.info("Deleting facility: {}", id);
        
        facilityService.deleteFacility(id);
        
        ApiResponse<Void> response = ApiResponse.<Void>builder()
                .success(true)
                .message("Facility deleted successfully")
                .build();
        
        return ResponseEntity.ok(response);
    }

    /**
     * Restore facility
     * POST /api/v1/facilities/{id}/restore
     */
    @PostMapping("/{id}/restore")
    @Operation(summary = "Restore facility", description = "Restores a soft-deleted facility")
    public ResponseEntity<ApiResponse<FacilityResponse>> restoreFacility(
            @Parameter(description = "Facility ID") @PathVariable Long id) {
        
        log.info("Restoring facility: {}", id);
        
        FacilityResponse facility = facilityService.restoreFacility(id);
        
        ApiResponse<FacilityResponse> response = ApiResponse.<FacilityResponse>builder()
                .success(true)
                .message("Facility restored successfully")
                .data(facility)
                .build();
        
        return ResponseEntity.ok(response);
    }
}

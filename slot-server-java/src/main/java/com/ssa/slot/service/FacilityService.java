package com.ssa.slot.service;

import com.ssa.slot.entity.Facility;
import com.ssa.slot.repository.FacilityRepository;
import com.ssa.slot.dto.FacilityCreateRequest;
import com.ssa.slot.dto.FacilityUpdateRequest;
import com.ssa.slot.dto.FacilityResponse;
import com.ssa.slot.dto.FacilitySummaryResponse;
import com.ssa.slot.exception.ResourceNotFoundException;
import com.ssa.slot.exception.DuplicateResourceException;
import com.ssa.slot.mapper.FacilityMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Facility Service
 * 
 * Replaces the C++ SLOTDataMgr facility-related functionality.
 * Provides business logic for facility management operations.
 * 
 * Original C++ equivalent: Server/SLOTDataMgr.h/cpp (facility methods)
 * 
 * Key improvements over C++:
 * - Declarative transaction management
 * - Caching support for performance
 * - DTO mapping for clean API contracts
 * - Comprehensive validation and error handling
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class FacilityService {

    private final FacilityRepository facilityRepository;
    private final FacilityMapper facilityMapper;

    /**
     * Create a new facility
     * Replaces C++ facility creation logic
     */
    @CacheEvict(value = "facilities", allEntries = true)
    public FacilityResponse createFacility(FacilityCreateRequest request) {
        log.info("Creating new facility: {}", request.getName());

        // Check for duplicate name
        if (facilityRepository.findByNameIgnoreCase(request.getName()).isPresent()) {
            throw new DuplicateResourceException("Facility with name '" + request.getName() + "' already exists");
        }

        // Map request to entity
        Facility facility = facilityMapper.toEntity(request);
        
        // Set default values
        if (facility.getStatus() == null) {
            facility.setStatus(Facility.FacilityStatus.ACTIVE);
        }
        if (facility.getIntegrated() == null) {
            facility.setIntegrated(false);
        }

        // Save facility
        Facility savedFacility = facilityRepository.save(facility);
        
        log.info("Created facility with ID: {}", savedFacility.getId());
        return facilityMapper.toResponse(savedFacility);
    }

    /**
     * Update an existing facility
     * Replaces C++ facility update logic
     */
    @CacheEvict(value = {"facilities", "facility"}, allEntries = true)
    public FacilityResponse updateFacility(Long facilityId, FacilityUpdateRequest request) {
        log.info("Updating facility ID: {}", facilityId);

        Facility facility = getFacilityEntity(facilityId);

        // Check for duplicate name (excluding current facility)
        if (!facility.getName().equalsIgnoreCase(request.getName()) &&
            facilityRepository.existsByNameIgnoreCaseAndIdNot(request.getName(), facilityId)) {
            throw new DuplicateResourceException("Facility with name '" + request.getName() + "' already exists");
        }

        // Update facility fields
        facilityMapper.updateEntity(request, facility);
        
        Facility updatedFacility = facilityRepository.save(facility);
        
        log.info("Updated facility ID: {}", facilityId);
        return facilityMapper.toResponse(updatedFacility);
    }

    /**
     * Get facility by ID
     * Replaces C++ GetFacilityByID functionality
     */
    @Cacheable(value = "facility", key = "#facilityId")
    @Transactional(readOnly = true)
    public FacilityResponse getFacility(Long facilityId) {
        log.debug("Retrieving facility ID: {}", facilityId);
        
        Facility facility = getFacilityEntity(facilityId);
        return facilityMapper.toResponse(facility);
    }

    /**
     * Get all facilities with pagination
     * Replaces C++ GetAllFacilities functionality
     */
    @Cacheable(value = "facilities")
    @Transactional(readOnly = true)
    public Page<FacilityResponse> getAllFacilities(Pageable pageable) {
        log.debug("Retrieving all facilities with pagination");
        
        Page<Facility> facilities = facilityRepository.findAll(pageable);
        return facilities.map(facilityMapper::toResponse);
    }

    /**
     * Get active facilities
     * Replaces C++ filtered queries for active facilities
     */
    @Cacheable(value = "activeFacilities")
    @Transactional(readOnly = true)
    public List<FacilityResponse> getActiveFacilities() {
        log.debug("Retrieving active facilities");
        
        List<Facility> facilities = facilityRepository.findActiveFacilities();
        return facilityMapper.toResponseList(facilities);
    }

    /**
     * Search facilities by criteria
     * Replaces C++ complex query building logic
     */
    @Transactional(readOnly = true)
    public Page<FacilityResponse> searchFacilities(
            String name, 
            Facility.FacilityStatus status, 
            String facilityType, 
            Boolean integrated, 
            Pageable pageable) {
        
        log.debug("Searching facilities with criteria - name: {}, status: {}, type: {}, integrated: {}", 
                  name, status, facilityType, integrated);
        
        Page<Facility> facilities = facilityRepository.findFacilitiesByCriteria(
                name, status, facilityType, integrated, pageable);
        
        return facilities.map(facilityMapper::toResponse);
    }

    /**
     * Get facility summary with counts
     * Replaces C++ aggregate queries for facility statistics
     */
    @Cacheable(value = "facilitySummary", key = "#facilityId")
    @Transactional(readOnly = true)
    public FacilitySummaryResponse getFacilitySummary(Long facilityId) {
        log.debug("Retrieving facility summary for ID: {}", facilityId);
        
        Facility facility = getFacilityEntity(facilityId);
        
        // Get counts
        long locationCount = facilityRepository.countLocationsByFacilityId(facilityId);
        long productCount = facilityRepository.countProductsByFacilityId(facilityId);
        
        return FacilitySummaryResponse.builder()
                .facility(facilityMapper.toResponse(facility))
                .totalLocations(locationCount)
                .totalProducts(productCount)
                .totalSections(facility.getSections().size())
                .build();
    }

    /**
     * Check if facility is ready for optimization
     * Replaces C++ optimization readiness checks
     */
    @Transactional(readOnly = true)
    public boolean isFacilityReadyForOptimization(Long facilityId) {
        log.debug("Checking optimization readiness for facility ID: {}", facilityId);
        
        Facility facility = getFacilityEntity(facilityId);
        
        // Check if facility is active
        if (facility.getStatus() != Facility.FacilityStatus.ACTIVE) {
            return false;
        }
        
        // Check if facility has products with movement data
        long productCount = facilityRepository.countProductsByFacilityId(facilityId);
        if (productCount == 0) {
            return false;
        }
        
        // Check if facility has locations
        long locationCount = facilityRepository.countLocationsByFacilityId(facilityId);
        if (locationCount == 0) {
            return false;
        }
        
        return true;
    }

    /**
     * Get facilities ready for optimization
     * Useful for optimization scheduling and monitoring
     */
    @Cacheable(value = "optimizationReadyFacilities")
    @Transactional(readOnly = true)
    public List<FacilityResponse> getFacilitiesReadyForOptimization() {
        log.debug("Retrieving facilities ready for optimization");
        
        List<Facility> facilities = facilityRepository.findFacilitiesReadyForOptimization();
        return facilityMapper.toResponseList(facilities);
    }

    /**
     * Delete facility (soft delete)
     * Replaces C++ facility deletion logic
     */
    @CacheEvict(value = {"facilities", "facility", "activeFacilities"}, allEntries = true)
    public void deleteFacility(Long facilityId) {
        log.info("Deleting facility ID: {}", facilityId);
        
        Facility facility = getFacilityEntity(facilityId);
        
        // Perform soft delete
        facility.markAsDeleted();
        facilityRepository.save(facility);
        
        log.info("Deleted facility ID: {}", facilityId);
    }

    /**
     * Restore soft-deleted facility
     */
    @CacheEvict(value = {"facilities", "facility", "activeFacilities"}, allEntries = true)
    public FacilityResponse restoreFacility(Long facilityId) {
        log.info("Restoring facility ID: {}", facilityId);
        
        Facility facility = facilityRepository.findById(facilityId)
                .orElseThrow(() -> new ResourceNotFoundException("Facility not found with ID: " + facilityId));
        
        facility.restore();
        Facility restoredFacility = facilityRepository.save(facility);
        
        log.info("Restored facility ID: {}", facilityId);
        return facilityMapper.toResponse(restoredFacility);
    }

    /**
     * Update facility CAD file path
     * Replaces C++ CAD file management
     */
    @CacheEvict(value = "facility", key = "#facilityId")
    public void updateCadFilePath(Long facilityId, String cadFilePath) {
        log.info("Updating CAD file path for facility ID: {}", facilityId);
        
        Facility facility = getFacilityEntity(facilityId);
        facility.setCadFilePath(cadFilePath);
        facilityRepository.save(facility);
        
        log.info("Updated CAD file path for facility ID: {}", facilityId);
    }

    /**
     * Update facility integration status
     * Replaces C++ integration status management
     */
    @CacheEvict(value = "facility", key = "#facilityId")
    public void updateIntegrationStatus(Long facilityId, boolean integrated) {
        log.info("Updating integration status for facility ID: {} to {}", facilityId, integrated);
        
        Facility facility = getFacilityEntity(facilityId);
        facility.setIntegrated(integrated);
        facilityRepository.save(facility);
        
        log.info("Updated integration status for facility ID: {}", facilityId);
    }

    /**
     * Helper method to get facility entity with proper error handling
     */
    private Facility getFacilityEntity(Long facilityId) {
        return facilityRepository.findById(facilityId)
                .orElseThrow(() -> new ResourceNotFoundException("Facility not found with ID: " + facilityId));
    }
}

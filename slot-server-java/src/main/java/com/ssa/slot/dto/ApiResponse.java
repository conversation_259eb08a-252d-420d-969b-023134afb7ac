package com.ssa.slot.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Standard API Response Wrapper
 * 
 * Provides a consistent response format for all API endpoints.
 * Replaces C++ return codes and error handling.
 * 
 * Original C++ equivalent: Various return codes and error structures
 * 
 * Key improvements:
 * - Consistent response format
 * - Type-safe error handling
 * - Metadata support
 * - JSON serialization
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {

    /**
     * Indicates if the operation was successful
     */
    private boolean success;

    /**
     * Human-readable message describing the result
     */
    private String message;

    /**
     * The actual response data
     */
    private T data;

    /**
     * Error details (only present when success = false)
     */
    private ErrorDetails error;

    /**
     * Response metadata
     */
    private ResponseMetadata metadata;

    /**
     * Timestamp when the response was generated
     */
    @Builder.Default
    private LocalDateTime timestamp = LocalDateTime.now();

    /**
     * Error Details
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ErrorDetails {
        /**
         * Error code for programmatic handling
         */
        private String code;

        /**
         * Detailed error message
         */
        private String details;

        /**
         * Field-specific validation errors
         */
        private List<FieldError> fieldErrors;

        /**
         * Stack trace (only in development)
         */
        private String stackTrace;
    }

    /**
     * Field Error for validation failures
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FieldError {
        /**
         * Field name that failed validation
         */
        private String field;

        /**
         * Rejected value
         */
        private Object rejectedValue;

        /**
         * Error message
         */
        private String message;
    }

    /**
     * Response Metadata
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ResponseMetadata {
        /**
         * Request ID for tracing
         */
        private String requestId;

        /**
         * Processing time in milliseconds
         */
        private Long processingTime;

        /**
         * API version
         */
        private String version;

        /**
         * Pagination information (for paginated responses)
         */
        private PaginationInfo pagination;
    }

    /**
     * Pagination Information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PaginationInfo {
        /**
         * Current page number (0-based)
         */
        private int page;

        /**
         * Number of items per page
         */
        private int size;

        /**
         * Total number of elements
         */
        private long totalElements;

        /**
         * Total number of pages
         */
        private int totalPages;

        /**
         * Whether this is the first page
         */
        private boolean first;

        /**
         * Whether this is the last page
         */
        private boolean last;
    }

    // Convenience factory methods

    /**
     * Create a successful response with data
     */
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
                .success(true)
                .message("Operation completed successfully")
                .data(data)
                .build();
    }

    /**
     * Create a successful response with data and custom message
     */
    public static <T> ApiResponse<T> success(T data, String message) {
        return ApiResponse.<T>builder()
                .success(true)
                .message(message)
                .data(data)
                .build();
    }

    /**
     * Create a successful response without data
     */
    public static <T> ApiResponse<T> success(String message) {
        return ApiResponse.<T>builder()
                .success(true)
                .message(message)
                .build();
    }

    /**
     * Create an error response
     */
    public static <T> ApiResponse<T> error(String message) {
        return ApiResponse.<T>builder()
                .success(false)
                .message(message)
                .build();
    }

    /**
     * Create an error response with error details
     */
    public static <T> ApiResponse<T> error(String message, ErrorDetails error) {
        return ApiResponse.<T>builder()
                .success(false)
                .message(message)
                .error(error)
                .build();
    }

    /**
     * Create an error response with error code and details
     */
    public static <T> ApiResponse<T> error(String message, String errorCode, String errorDetails) {
        ErrorDetails error = ErrorDetails.builder()
                .code(errorCode)
                .details(errorDetails)
                .build();

        return ApiResponse.<T>builder()
                .success(false)
                .message(message)
                .error(error)
                .build();
    }

    /**
     * Create a validation error response
     */
    public static <T> ApiResponse<T> validationError(String message, List<FieldError> fieldErrors) {
        ErrorDetails error = ErrorDetails.builder()
                .code("VALIDATION_ERROR")
                .details("Request validation failed")
                .fieldErrors(fieldErrors)
                .build();

        return ApiResponse.<T>builder()
                .success(false)
                .message(message)
                .error(error)
                .build();
    }

    /**
     * Add metadata to the response
     */
    public ApiResponse<T> withMetadata(ResponseMetadata metadata) {
        this.metadata = metadata;
        return this;
    }

    /**
     * Add request ID to metadata
     */
    public ApiResponse<T> withRequestId(String requestId) {
        if (this.metadata == null) {
            this.metadata = new ResponseMetadata();
        }
        this.metadata.setRequestId(requestId);
        return this;
    }

    /**
     * Add processing time to metadata
     */
    public ApiResponse<T> withProcessingTime(Long processingTime) {
        if (this.metadata == null) {
            this.metadata = new ResponseMetadata();
        }
        this.metadata.setProcessingTime(processingTime);
        return this;
    }
}

package com.ssa.slot.dto;

import com.ssa.slot.entity.Facility;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * Facility Create Request DTO
 * 
 * Data Transfer Object for creating new facilities.
 * Replaces C++ parameter passing and validation.
 * 
 * Original C++ equivalent: Function parameters in facility creation methods
 * 
 * Key improvements:
 * - Bean validation annotations
 * - Clear API contract
 * - Type safety
 * - Documentation
 */
@Data
public class FacilityCreateRequest {

    /**
     * Facility name - must be unique
     */
    @NotBlank(message = "Facility name is required")
    @Size(max = 100, message = "Facility name must not exceed 100 characters")
    private String name;

    /**
     * Facility description
     */
    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    /**
     * Facility type (e.g., Distribution Center, Warehouse)
     */
    @Size(max = 50, message = "Facility type must not exceed 50 characters")
    private String facilityType;

    /**
     * Facility status
     */
    private Facility.FacilityStatus status = Facility.FacilityStatus.ACTIVE;

    /**
     * Physical dimensions of the facility
     */
    @Valid
    private DimensionsDto dimensions;

    /**
     * Address information
     */
    @Valid
    private AddressDto address;

    /**
     * CAD file path for facility layout
     */
    @Size(max = 500, message = "CAD file path must not exceed 500 characters")
    private String cadFilePath;

    /**
     * Whether facility is integrated with external systems
     */
    private Boolean integrated = false;

    /**
     * Embedded Dimensions DTO
     */
    @Data
    public static class DimensionsDto {
        private Double width;
        private Double height;
        private Double depth;
        private Double area;
        private Double volume;
    }

    /**
     * Embedded Address DTO
     */
    @Data
    public static class AddressDto {
        @Size(max = 200, message = "Street address must not exceed 200 characters")
        private String streetAddress;

        @Size(max = 100, message = "City must not exceed 100 characters")
        private String city;

        @Size(max = 50, message = "State must not exceed 50 characters")
        private String state;

        @Size(max = 20, message = "Postal code must not exceed 20 characters")
        private String postalCode;

        @Size(max = 50, message = "Country must not exceed 50 characters")
        private String country;
    }
}

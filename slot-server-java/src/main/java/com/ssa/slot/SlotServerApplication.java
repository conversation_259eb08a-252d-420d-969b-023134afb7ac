package com.ssa.slot;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * SLOT Server Application Main Class
 * 
 * This is the main entry point for the SLOT Warehouse Optimization Server.
 * It replaces the original C++ Server layer with a modern Java Spring Boot application.
 * 
 * Original C++ equivalent: Server/OptiServer/OptiServer.cpp (main entry point)
 * 
 * Features enabled:
 * - JPA Auditing for automatic timestamp management
 * - Caching for performance optimization
 * - Async processing for long-running operations
 * - Transaction management for data consistency
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableCaching
@EnableAsync
@EnableTransactionManagement
public class SlotServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(SlotServerApplication.class, args);
    }
}

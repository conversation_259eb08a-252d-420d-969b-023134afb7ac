package com.ssa.slot.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * Aisle Entity
 * 
 * Represents an aisle within a section.
 * Replaces the C++ SLOTAisle class.
 * 
 * Original C++ equivalent: Server/SLOTAisle.h/cpp
 * 
 * An aisle contains multiple bays and represents a physical corridor
 * in the warehouse where products are stored.
 */
@Entity
@Table(name = "slot_aisle")
@Data
@EqualsAndHashCode(callSuper = true)
public class Aisle extends BaseEntity {

    /**
     * Aisle name/identifier
     */
    @NotBlank(message = "Aisle name is required")
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    /**
     * Aisle code for quick reference
     */
    @Column(name = "aisle_code", length = 20)
    private String aisleCode;

    /**
     * Reference to the parent section
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "section_id", nullable = false)
    @NotNull(message = "Section is required")
    private Section section;

    /**
     * Physical coordinates and dimensions
     */
    @Embedded
    private AisleDimensions dimensions;

    /**
     * Aisle profile reference for configuration
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "aisle_profile_id")
    private AisleProfile aisleProfile;

    /**
     * Entry and exit points for the aisle
     */
    @Embedded
    private EntryExitPoints entryExitPoints;

    /**
     * Aisle status
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AisleStatus status = AisleStatus.ACTIVE;

    /**
     * One-to-many relationship with Bays
     */
    @OneToMany(mappedBy = "aisle", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Bay> bays = new ArrayList<>();

    /**
     * Embedded Aisle Dimensions
     */
    @Embeddable
    @Data
    public static class AisleDimensions {
        @Column(name = "length")
        private Double length;

        @Column(name = "width")
        private Double width;

        @Column(name = "height")
        private Double height;

        @Column(name = "x_coord")
        private Double xCoord;

        @Column(name = "y_coord")
        private Double yCoord;

        @Column(name = "z_coord")
        private Double zCoord;

        @Column(name = "rotation")
        private Double rotation;
    }

    /**
     * Embedded Entry/Exit Points
     */
    @Embeddable
    @Data
    public static class EntryExitPoints {
        @Column(name = "entry_x")
        private Double entryX;

        @Column(name = "entry_y")
        private Double entryY;

        @Column(name = "exit_x")
        private Double exitX;

        @Column(name = "exit_y")
        private Double exitY;
    }

    /**
     * Aisle Status Enum
     */
    public enum AisleStatus {
        ACTIVE,
        INACTIVE,
        UNDER_CONSTRUCTION,
        MAINTENANCE,
        BLOCKED
    }

    /**
     * Helper method to add a bay
     */
    public void addBay(Bay bay) {
        bays.add(bay);
        bay.setAisle(this);
    }

    /**
     * Helper method to remove a bay
     */
    public void removeBay(Bay bay) {
        bays.remove(bay);
        bay.setAisle(null);
    }

    /**
     * Get total number of locations in this aisle
     */
    public long getTotalLocationCount() {
        return bays.stream()
                .flatMap(bay -> bay.getLevels().stream())
                .mapToLong(level -> level.getLocations().size())
                .sum();
    }

    /**
     * Check if aisle has entry/exit points defined
     */
    public boolean hasEntryExitPoints() {
        return entryExitPoints != null && 
               entryExitPoints.getEntryX() != null && 
               entryExitPoints.getEntryY() != null &&
               entryExitPoints.getExitX() != null && 
               entryExitPoints.getExitY() != null;
    }

    /**
     * Check if aisle has dimensions defined
     */
    public boolean hasDimensions() {
        return dimensions != null && 
               dimensions.getLength() != null && 
               dimensions.getWidth() != null;
    }

    /**
     * Get the facility this aisle belongs to
     */
    public Facility getFacility() {
        return section != null ? section.getFacility() : null;
    }
}

package com.ssa.slot.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * Base Entity Class
 * 
 * Replaces the C++ SLOTDBObject base class functionality.
 * Provides common fields and behavior for all database entities.
 * 
 * Original C++ equivalent: Server/SLOTDBObject.h/cpp
 * 
 * Key differences from C++:
 * - Uses JPA annotations instead of manual SQL mapping
 * - Automatic auditing with @CreatedDate and @LastModifiedDate
 * - Lombok for reducing boilerplate code
 * - Generic ID type support
 */
@Data
@EqualsAndHashCode(callSuper = false)
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class BaseEntity {

    /**
     * Primary key - equivalent to C++ DBID field
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * Description field - equivalent to C++ Description field
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * Parent ID for hierarchical relationships
     * Equivalent to C++ ParentDBID field
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * Audit fields - automatic timestamp management
     * These replace manual timestamp handling in C++
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * Version field for optimistic locking
     * Helps prevent concurrent modification issues
     */
    @Version
    @Column(name = "version")
    private Long version;

    /**
     * Soft delete flag
     * Allows logical deletion without physical removal
     */
    @Column(name = "deleted", nullable = false)
    private Boolean deleted = false;

    /**
     * User who created this record
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * User who last modified this record
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * Pre-persist callback to set default values
     */
    @PrePersist
    protected void onCreate() {
        if (deleted == null) {
            deleted = false;
        }
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
    }

    /**
     * Pre-update callback to update timestamp
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * Check if entity is new (not persisted yet)
     */
    public boolean isNew() {
        return id == null;
    }

    /**
     * Soft delete this entity
     */
    public void markAsDeleted() {
        this.deleted = true;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Restore soft deleted entity
     */
    public void restore() {
        this.deleted = false;
        this.updatedAt = LocalDateTime.now();
    }
}

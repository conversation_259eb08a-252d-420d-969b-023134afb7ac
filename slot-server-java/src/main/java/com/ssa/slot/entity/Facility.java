package com.ssa.slot.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * Facility Entity
 * 
 * Represents a warehouse facility in the SLOT system.
 * Replaces the C++ SLOTFacility class.
 * 
 * Original C++ equivalent: Server/SLOTFacility.h/cpp
 * 
 * Key improvements over C++:
 * - JPA entity mapping with automatic relationship management
 * - Bean validation for data integrity
 * - Embedded value objects for complex types
 * - Lazy loading for performance optimization
 */
@Entity
@Table(name = "slot_facility")
@Data
@EqualsAndHashCode(callSuper = true)
public class Facility extends BaseEntity {

    /**
     * Facility name - must be unique and not blank
     */
    @NotBlank(message = "Facility name is required")
    @Column(name = "name", nullable = false, unique = true, length = 100)
    private String name;

    /**
     * Facility type (e.g., Distribution Center, Warehouse, etc.)
     */
    @Column(name = "facility_type", length = 50)
    private String facilityType;

    /**
     * Physical dimensions of the facility
     */
    @Embedded
    private Dimensions dimensions;

    /**
     * Address information
     */
    @Embedded
    private Address address;

    /**
     * Facility status (Active, Inactive, Under Construction, etc.)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private FacilityStatus status = FacilityStatus.ACTIVE;

    /**
     * CAD file path for facility layout
     * Equivalent to C++ CAD file handling
     */
    @Column(name = "cad_file_path", length = 500)
    private String cadFilePath;

    /**
     * Maximum number identifier for this facility
     * Equivalent to C++ MaxNum field
     */
    @Column(name = "max_num")
    private Integer maxNum;

    /**
     * Whether facility is integrated with external systems
     */
    @Column(name = "integrated", nullable = false)
    private Boolean integrated = false;

    /**
     * One-to-many relationship with Sections
     * Lazy loading for performance
     */
    @OneToMany(mappedBy = "facility", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Section> sections = new ArrayList<>();

    /**
     * One-to-many relationship with Products
     */
    @OneToMany(mappedBy = "facility", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ProductPack> products = new ArrayList<>();

    /**
     * One-to-many relationship with Hotspots
     */
    @OneToMany(mappedBy = "facility", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Hotspot> hotspots = new ArrayList<>();

    /**
     * Embedded Dimensions class
     * Replaces individual width/height/depth fields from C++
     */
    @Embeddable
    @Data
    public static class Dimensions {
        @Column(name = "width")
        private Double width;

        @Column(name = "height")
        private Double height;

        @Column(name = "depth")
        private Double depth;

        @Column(name = "area")
        private Double area;

        @Column(name = "volume")
        private Double volume;

        /**
         * Calculate area if not set
         */
        public Double getCalculatedArea() {
            if (area != null) return area;
            if (width != null && depth != null) {
                return width * depth;
            }
            return null;
        }

        /**
         * Calculate volume if not set
         */
        public Double getCalculatedVolume() {
            if (volume != null) return volume;
            if (width != null && height != null && depth != null) {
                return width * height * depth;
            }
            return null;
        }
    }

    /**
     * Embedded Address class
     */
    @Embeddable
    @Data
    public static class Address {
        @Column(name = "street_address", length = 200)
        private String streetAddress;

        @Column(name = "city", length = 100)
        private String city;

        @Column(name = "state", length = 50)
        private String state;

        @Column(name = "postal_code", length = 20)
        private String postalCode;

        @Column(name = "country", length = 50)
        private String country;
    }

    /**
     * Facility Status Enum
     */
    public enum FacilityStatus {
        ACTIVE,
        INACTIVE,
        UNDER_CONSTRUCTION,
        MAINTENANCE,
        DECOMMISSIONED
    }

    /**
     * Helper method to add a section
     */
    public void addSection(Section section) {
        sections.add(section);
        section.setFacility(this);
    }

    /**
     * Helper method to remove a section
     */
    public void removeSection(Section section) {
        sections.remove(section);
        section.setFacility(null);
    }

    /**
     * Helper method to add a product
     */
    public void addProduct(ProductPack product) {
        products.add(product);
        product.setFacility(this);
    }

    /**
     * Helper method to remove a product
     */
    public void removeProduct(ProductPack product) {
        products.remove(product);
        product.setFacility(null);
    }

    /**
     * Get total number of locations in this facility
     */
    public long getTotalLocationCount() {
        return sections.stream()
                .flatMap(section -> section.getAisles().stream())
                .flatMap(aisle -> aisle.getBays().stream())
                .flatMap(bay -> bay.getLevels().stream())
                .mapToLong(level -> level.getLocations().size())
                .sum();
    }

    /**
     * Check if facility has CAD file
     */
    public boolean hasCadFile() {
        return cadFilePath != null && !cadFilePath.trim().isEmpty();
    }
}

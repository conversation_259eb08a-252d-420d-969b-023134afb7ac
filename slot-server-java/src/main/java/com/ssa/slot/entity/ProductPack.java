package com.ssa.slot.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * Product Pack Entity
 * 
 * Represents a product package in the SLOT system.
 * Replaces the C++ SLOTProductPack class.
 * 
 * Original C++ equivalent: Server/SLOTProductPack.h/cpp
 * 
 * Key improvements over C++:
 * - JPA entity mapping with validation
 * - BigDecimal for precise financial calculations
 * - Embedded dimensions object
 * - Automatic cube calculations
 */
@Entity
@Table(name = "slot_product_pack", 
       indexes = {
           @Index(name = "idx_product_sku", columnList = "sku"),
           @Index(name = "idx_product_facility", columnList = "facility_id"),
           @Index(name = "idx_product_movement", columnList = "movement")
       })
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductPack extends BaseEntity {

    /**
     * Stock Keeping Unit - unique identifier for the product
     */
    @NotBlank(message = "SKU is required")
    @Column(name = "sku", nullable = false, length = 50)
    private String sku;

    /**
     * Product description
     */
    @Column(name = "product_description", length = 500)
    private String productDescription;

    /**
     * WMS (Warehouse Management System) ID
     */
    @Column(name = "wms_id", length = 50)
    private String wmsId;

    /**
     * WMS Detail ID
     */
    @Column(name = "wms_detail_id", length = 50)
    private String wmsDetailId;

    /**
     * Product movement (cases per time period)
     * Critical for optimization algorithms
     */
    @PositiveOrZero(message = "Movement must be positive or zero")
    @Column(name = "movement", precision = 15, scale = 4)
    private BigDecimal movement = BigDecimal.ZERO;

    /**
     * Balance on Hand (current inventory)
     */
    @PositiveOrZero(message = "Balance on hand must be positive or zero")
    @Column(name = "balance_on_hand", precision = 15, scale = 4)
    private BigDecimal balanceOnHand = BigDecimal.ZERO;

    /**
     * Unit of Issue (cases per pallet, etc.)
     */
    @PositiveOrZero(message = "Unit of issue must be positive or zero")
    @Column(name = "unit_of_issue")
    private Integer unitOfIssue = 1;

    /**
     * Product weight
     */
    @PositiveOrZero(message = "Weight must be positive or zero")
    @Column(name = "weight", precision = 10, scale = 4)
    private BigDecimal weight = BigDecimal.ZERO;

    /**
     * Hazard flag for special handling requirements
     */
    @Column(name = "hazard_flag", nullable = false)
    private Boolean hazardFlag = false;

    /**
     * Physical dimensions of the product package
     */
    @Embedded
    private ProductDimensions dimensions;

    /**
     * Calculated cubes - core to SLOT optimization
     */
    @Embedded
    private CubeCalculations cubeCalculations;

    /**
     * Reference to the facility this product belongs to
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "facility_id", nullable = false)
    @NotNull(message = "Facility is required")
    private Facility facility;

    /**
     * Current location assignment (if any)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "location_id")
    private Location currentLocation;

    /**
     * Product category for grouping
     */
    @Column(name = "category", length = 100)
    private String category;

    /**
     * Product status
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ProductStatus status = ProductStatus.ACTIVE;

    /**
     * Embedded Product Dimensions
     */
    @Embeddable
    @Data
    public static class ProductDimensions {
        @Column(name = "width", precision = 10, scale = 4)
        private BigDecimal width;

        @Column(name = "height", precision = 10, scale = 4)
        private BigDecimal height;

        @Column(name = "depth", precision = 10, scale = 4)
        private BigDecimal depth;

        /**
         * Calculate basic cube (width * height * depth)
         */
        public BigDecimal getBasicCube() {
            if (width != null && height != null && depth != null) {
                return width.multiply(height).multiply(depth);
            }
            return BigDecimal.ZERO;
        }
    }

    /**
     * Embedded Cube Calculations
     * Core to SLOT optimization algorithms
     */
    @Embeddable
    @Data
    public static class CubeCalculations {
        /**
         * Basic cube (width * height * depth)
         */
        @Column(name = "basic_cube", precision = 15, scale = 6)
        private BigDecimal basicCube;

        /**
         * Extended cube (basic cube * movement)
         * Used in Pass1 optimization
         */
        @Column(name = "extended_cube", precision = 15, scale = 6)
        private BigDecimal extendedCube;

        /**
         * Inventory cube (basic cube * balance on hand)
         * Used in Pass4 optimization
         */
        @Column(name = "inventory_cube", precision = 15, scale = 6)
        private BigDecimal inventoryCube;

        /**
         * Case cubic feet
         */
        @Column(name = "case_cube", precision = 15, scale = 6)
        private BigDecimal caseCube;
    }

    /**
     * Product Status Enum
     */
    public enum ProductStatus {
        ACTIVE,
        INACTIVE,
        DISCONTINUED,
        PENDING_APPROVAL
    }

    /**
     * Calculate and update all cube values
     * This method implements the core cube calculation logic from C++
     */
    public void calculateCubes() {
        if (dimensions == null || cubeCalculations == null) {
            return;
        }

        // Calculate basic cube
        BigDecimal basicCube = dimensions.getBasicCube();
        cubeCalculations.setBasicCube(basicCube);

        // Calculate extended cube (basic cube * movement)
        if (movement != null && movement.compareTo(BigDecimal.ZERO) > 0) {
            cubeCalculations.setExtendedCube(basicCube.multiply(movement));
        } else {
            cubeCalculations.setExtendedCube(BigDecimal.ZERO);
        }

        // Calculate inventory cube (basic cube * balance on hand)
        if (balanceOnHand != null && balanceOnHand.compareTo(BigDecimal.ZERO) > 0) {
            cubeCalculations.setInventoryCube(basicCube.multiply(balanceOnHand));
        } else {
            cubeCalculations.setInventoryCube(BigDecimal.ZERO);
        }
    }

    /**
     * Pre-persist and pre-update callbacks to ensure cubes are calculated
     */
    @PrePersist
    @PreUpdate
    protected void updateCalculations() {
        if (dimensions == null) {
            dimensions = new ProductDimensions();
        }
        if (cubeCalculations == null) {
            cubeCalculations = new CubeCalculations();
        }
        calculateCubes();
    }

    /**
     * Check if product has valid dimensions
     */
    public boolean hasValidDimensions() {
        return dimensions != null && 
               dimensions.getWidth() != null && 
               dimensions.getHeight() != null && 
               dimensions.getDepth() != null &&
               dimensions.getWidth().compareTo(BigDecimal.ZERO) > 0 &&
               dimensions.getHeight().compareTo(BigDecimal.ZERO) > 0 &&
               dimensions.getDepth().compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Check if product has movement data
     */
    public boolean hasMovement() {
        return movement != null && movement.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Check if product is assigned to a location
     */
    public boolean isAssigned() {
        return currentLocation != null;
    }

    /**
     * Get extended cube value safely
     */
    public BigDecimal getExtendedCube() {
        return cubeCalculations != null && cubeCalculations.getExtendedCube() != null 
               ? cubeCalculations.getExtendedCube() 
               : BigDecimal.ZERO;
    }

    /**
     * Get inventory cube value safely
     */
    public BigDecimal getInventoryCube() {
        return cubeCalculations != null && cubeCalculations.getInventoryCube() != null 
               ? cubeCalculations.getInventoryCube() 
               : BigDecimal.ZERO;
    }
}

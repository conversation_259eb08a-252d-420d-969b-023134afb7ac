package com.ssa.slot.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * Section Entity
 * 
 * Represents a section within a facility.
 * Replaces the C++ SLOTSection class.
 * 
 * Original C++ equivalent: Server/SLOTSection.h/cpp
 * 
 * A section is a logical grouping of aisles within a facility.
 * Hierarchy: Facility -> Section -> Aisle -> Bay -> Level -> Location
 */
@Entity
@Table(name = "slot_section")
@Data
@EqualsAndHashCode(callSuper = true)
public class Section extends BaseEntity {

    /**
     * Section name/identifier
     */
    @NotBlank(message = "Section name is required")
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    /**
     * Section code for quick reference
     */
    @Column(name = "section_code", length = 20)
    private String sectionCode;

    /**
     * Reference to the parent facility
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "facility_id", nullable = false)
    @NotNull(message = "Facility is required")
    private Facility facility;

    /**
     * Physical coordinates within the facility
     */
    @Embedded
    private Coordinates coordinates;

    /**
     * Section type (e.g., Pick, Reserve, Bulk, etc.)
     */
    @Column(name = "section_type", length = 50)
    private String sectionType;

    /**
     * Section status
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private SectionStatus status = SectionStatus.ACTIVE;

    /**
     * One-to-many relationship with Aisles
     */
    @OneToMany(mappedBy = "section", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Aisle> aisles = new ArrayList<>();

    /**
     * One-to-many relationship with Hotspots
     */
    @OneToMany(mappedBy = "section", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Hotspot> hotspots = new ArrayList<>();

    /**
     * Embedded Coordinates class
     */
    @Embeddable
    @Data
    public static class Coordinates {
        @Column(name = "x_coord")
        private Double xCoord;

        @Column(name = "y_coord")
        private Double yCoord;

        @Column(name = "z_coord")
        private Double zCoord;

        @Column(name = "rotation")
        private Double rotation;
    }

    /**
     * Section Status Enum
     */
    public enum SectionStatus {
        ACTIVE,
        INACTIVE,
        UNDER_CONSTRUCTION,
        MAINTENANCE
    }

    /**
     * Helper method to add an aisle
     */
    public void addAisle(Aisle aisle) {
        aisles.add(aisle);
        aisle.setSection(this);
    }

    /**
     * Helper method to remove an aisle
     */
    public void removeAisle(Aisle aisle) {
        aisles.remove(aisle);
        aisle.setSection(null);
    }

    /**
     * Helper method to add a hotspot
     */
    public void addHotspot(Hotspot hotspot) {
        hotspots.add(hotspot);
        hotspot.setSection(this);
    }

    /**
     * Helper method to remove a hotspot
     */
    public void removeHotspot(Hotspot hotspot) {
        hotspots.remove(hotspot);
        hotspot.setSection(null);
    }

    /**
     * Get total number of locations in this section
     */
    public long getTotalLocationCount() {
        return aisles.stream()
                .flatMap(aisle -> aisle.getBays().stream())
                .flatMap(bay -> bay.getLevels().stream())
                .mapToLong(level -> level.getLocations().size())
                .sum();
    }

    /**
     * Get total number of bays in this section
     */
    public long getTotalBayCount() {
        return aisles.stream()
                .mapToLong(aisle -> aisle.getBays().size())
                .sum();
    }

    /**
     * Check if section has coordinates
     */
    public boolean hasCoordinates() {
        return coordinates != null && 
               coordinates.getXCoord() != null && 
               coordinates.getYCoord() != null;
    }
}

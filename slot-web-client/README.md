# SLOT Web Client - React Implementation

## 概述

这是SLOT仓储优化系统的现代化Web客户端，使用React + TypeScript构建。该项目将原有的C++ MFC/AutoCAD客户端重写为现代化的Web应用，提供更好的用户体验和跨平台支持。

## 项目结构

```
slot-web-client/
├── public/                             # 静态资源
│   ├── index.html                      # HTML模板
│   ├── manifest.json                   # PWA配置
│   └── icons/                          # 应用图标
├── src/
│   ├── components/                     # 可复用组件
│   │   ├── common/                     # 通用组件
│   │   ├── facilities/                 # 设施相关组件
│   │   ├── products/                   # 产品相关组件
│   │   ├── optimization/               # 优化相关组件
│   │   ├── visualization/              # 可视化组件
│   │   └── Layout/                     # 布局组件
│   ├── pages/                          # 页面组件
│   │   ├── auth/                       # 认证页面
│   │   ├── dashboard/                  # 仪表板
│   │   ├── facilities/                 # 设施管理
│   │   ├── products/                   # 产品管理
│   │   ├── optimization/               # 优化管理
│   │   ├── reports/                    # 报表中心
│   │   └── settings/                   # 系统设置
│   ├── hooks/                          # 自定义Hook
│   ├── services/                       # API服务
│   │   ├── api/                        # API客户端
│   │   ├── auth/                       # 认证服务
│   │   └── cache/                      # 缓存服务
│   ├── store/                          # 状态管理
│   ├── types/                          # TypeScript类型定义
│   ├── utils/                          # 工具函数
│   ├── styles/                         # 样式文件
│   ├── contexts/                       # React Context
│   ├── App.tsx                         # 应用程序入口
│   └── main.tsx                        # 应用启动文件
├── package.json                        # 项目配置
├── vite.config.ts                      # Vite配置
├── tsconfig.json                       # TypeScript配置
└── README.md                           # 项目说明
```

## 技术栈

### 核心框架
- **React 18** - 用户界面库
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 现代化构建工具
- **React Router** - 客户端路由

### UI组件库
- **Ant Design** - 企业级UI组件库
- **Ant Design Pro Components** - 高级业务组件
- **@ant-design/icons** - 图标库

### 数据管理
- **React Query** - 服务器状态管理
- **Zustand** - 客户端状态管理
- **Axios** - HTTP客户端

### 可视化
- **ECharts** - 图表库
- **Three.js** - 3D可视化
- **React Three Fiber** - React Three.js集成
- **Konva.js** - 2D Canvas库
- **D3.js** - 数据可视化

### 开发工具
- **ESLint** - 代码检查
- **Prettier** - 代码格式化
- **Vitest** - 单元测试
- **Storybook** - 组件开发

## 快速开始

### 环境要求
- Node.js 18+
- npm 9+ 或 yarn 1.22+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd slot-web-client
```

2. **安装依赖**
```bash
npm install
# 或
yarn install
```

3. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量
vim .env.local
```

4. **启动开发服务器**
```bash
npm run dev
# 或
yarn dev
```

5. **访问应用**
- 开发服务器: http://localhost:3000
- API代理: http://localhost:3000/api -> http://localhost:8080/slot-server/api

### 环境变量配置

```bash
# .env.local
VITE_API_BASE_URL=http://localhost:8080/slot-server/api
VITE_WS_BASE_URL=ws://localhost:8080/slot-server/ws
VITE_APP_TITLE=SLOT仓储优化系统
VITE_APP_VERSION=1.0.0
```

## 功能特性

### 1. 设施管理
- ✅ 设施列表和搜索
- ✅ 设施创建和编辑
- ✅ 设施详情查看
- ✅ 3D设施可视化
- ✅ 设施状态管理
- ✅ 批量操作

### 2. 产品管理
- ✅ 产品列表和搜索
- ✅ 产品导入导出
- ✅ 立方体计算
- ✅ 产品分组管理
- ✅ 移动量分析

### 3. 优化管理
- ✅ Pass1-Pass5优化算法
- ✅ 参数配置界面
- ✅ 实时进度监控
- ✅ 结果可视化
- ✅ 历史记录管理

### 4. 可视化功能
- ✅ 3D设施布局
- ✅ 热力图显示
- ✅ 交互式图表
- ✅ 实时数据更新
- ✅ 导出功能

### 5. 报表中心
- ✅ 25种报表类型
- ✅ 自定义报表
- ✅ Excel/PDF导出
- ✅ 报表调度
- ✅ 数据分析

## 开发指南

### 组件开发

#### 创建新组件
```tsx
// src/components/example/ExampleComponent.tsx
import React from 'react'
import { Card, Button } from 'antd'

interface ExampleComponentProps {
  title: string
  onAction?: () => void
}

const ExampleComponent: React.FC<ExampleComponentProps> = ({
  title,
  onAction,
}) => {
  return (
    <Card title={title}>
      <Button onClick={onAction}>
        执行操作
      </Button>
    </Card>
  )
}

export default ExampleComponent
```

#### 使用自定义Hook
```tsx
// src/hooks/useExample.ts
import { useState, useEffect } from 'react'
import { api } from '@/services/api'

export const useExample = () => {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(false)

  const fetchData = async () => {
    setLoading(true)
    try {
      const result = await api.get('/example')
      setData(result)
    } catch (error) {
      console.error('Failed to fetch data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  return { data, loading, refetch: fetchData }
}
```

### API集成

#### 定义API服务
```tsx
// src/services/api/exampleApi.ts
import { api } from './apiClient'
import { Example, ExampleCreateRequest } from '@/types/example'

export const exampleApi = {
  getAll: (): Promise<Example[]> => 
    api.get('/examples'),

  getById: (id: number): Promise<Example> => 
    api.get(`/examples/${id}`),

  create: (data: ExampleCreateRequest): Promise<Example> => 
    api.post('/examples', data),

  update: (id: number, data: Partial<Example>): Promise<Example> => 
    api.put(`/examples/${id}`, data),

  delete: (id: number): Promise<void> => 
    api.delete(`/examples/${id}`),
}
```

#### 使用React Query
```tsx
// src/hooks/useExamples.ts
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { exampleApi } from '@/services/api/exampleApi'

export const useExamples = () => {
  return useQuery({
    queryKey: ['examples'],
    queryFn: exampleApi.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export const useCreateExample = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: exampleApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries(['examples'])
    },
  })
}
```

### 状态管理

#### Zustand Store
```tsx
// src/store/exampleStore.ts
import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'

interface ExampleState {
  items: Example[]
  selectedItem: Example | null
  setItems: (items: Example[]) => void
  setSelectedItem: (item: Example | null) => void
  addItem: (item: Example) => void
  updateItem: (id: number, updates: Partial<Example>) => void
  removeItem: (id: number) => void
}

export const useExampleStore = create<ExampleState>()(
  immer((set) => ({
    items: [],
    selectedItem: null,
    setItems: (items) => set((state) => {
      state.items = items
    }),
    setSelectedItem: (item) => set((state) => {
      state.selectedItem = item
    }),
    addItem: (item) => set((state) => {
      state.items.push(item)
    }),
    updateItem: (id, updates) => set((state) => {
      const index = state.items.findIndex(item => item.id === id)
      if (index !== -1) {
        Object.assign(state.items[index], updates)
      }
    }),
    removeItem: (id) => set((state) => {
      state.items = state.items.filter(item => item.id !== id)
    }),
  }))
)
```

### 测试

#### 组件测试
```tsx
// src/components/example/__tests__/ExampleComponent.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import ExampleComponent from '../ExampleComponent'

describe('ExampleComponent', () => {
  it('renders with title', () => {
    render(<ExampleComponent title="Test Title" />)
    expect(screen.getByText('Test Title')).toBeInTheDocument()
  })

  it('calls onAction when button is clicked', () => {
    const onAction = vi.fn()
    render(<ExampleComponent title="Test" onAction={onAction} />)
    
    fireEvent.click(screen.getByText('执行操作'))
    expect(onAction).toHaveBeenCalledTimes(1)
  })
})
```

#### Hook测试
```tsx
// src/hooks/__tests__/useExample.test.ts
import { renderHook, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { useExample } from '../useExample'

// Mock API
vi.mock('@/services/api', () => ({
  api: {
    get: vi.fn(),
  },
}))

describe('useExample', () => {
  it('fetches data on mount', async () => {
    const mockData = { id: 1, name: 'Test' }
    vi.mocked(api.get).mockResolvedValue(mockData)

    const { result } = renderHook(() => useExample())

    expect(result.current.loading).toBe(true)

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
      expect(result.current.data).toEqual(mockData)
    })
  })
})
```

## 构建和部署

### 开发构建
```bash
# 开发模式
npm run dev

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 运行测试
npm run test
```

### 生产构建
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### Docker部署
```bash
# 构建Docker镜像
docker build -t slot-web-client .

# 运行容器
docker run -p 80:80 slot-web-client
```

### Nginx配置
```nginx
# nginx.conf
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://backend:8080/slot-server/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 性能优化

### 代码分割
```tsx
// 路由级别的代码分割
import { lazy, Suspense } from 'react'

const FacilitiesPage = lazy(() => import('@/pages/facilities/FacilitiesPage'))

// 使用
<Route
  path="/facilities"
  element={
    <Suspense fallback={<Loading />}>
      <FacilitiesPage />
    </Suspense>
  }
/>
```

### 虚拟滚动
```tsx
// 大数据列表优化
import { FixedSizeList as List } from 'react-window'

const VirtualTable: React.FC = ({ data }) => (
  <List
    height={600}
    itemCount={data.length}
    itemSize={50}
    itemData={data}
  >
    {({ index, style, data }) => (
      <div style={style}>
        <TableRow data={data[index]} />
      </div>
    )}
  </List>
)
```

### 缓存策略
```tsx
// React Query缓存配置
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
      refetchOnWindowFocus: false,
    },
  },
})
```

## 故障排除

### 常见问题

1. **API请求失败**
   - 检查后端服务是否启动
   - 验证API地址配置
   - 查看浏览器网络面板

2. **路由不工作**
   - 确认React Router配置
   - 检查Nginx配置的SPA支持
   - 验证路由路径

3. **构建失败**
   - 清理node_modules重新安装
   - 检查TypeScript类型错误
   - 验证依赖版本兼容性

### 调试工具

- **React DevTools** - React组件调试
- **Redux DevTools** - 状态管理调试
- **React Query DevTools** - 数据获取调试
- **Vite DevTools** - 构建工具调试

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

[许可证信息]

## 联系方式

- 项目维护者: [维护者信息]
- 问题反馈: [Issue链接]
- 文档: [文档链接]

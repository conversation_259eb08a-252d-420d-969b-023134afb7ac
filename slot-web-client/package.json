{"name": "slot-web-client", "version": "1.0.0", "description": "SLOT Warehouse Optimization Web Client - React Implementation", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "axios": "^1.6.2", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "^2.6.48", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "three": "^0.158.0", "@react-three/fiber": "^8.15.11", "@react-three/drei": "^9.88.13", "konva": "^9.2.0", "react-konva": "^18.2.10", "fabric": "^5.3.0", "d3": "^7.8.5", "@types/d3": "^7.4.3", "lodash": "^4.17.21", "@types/lodash": "^4.14.202", "dayjs": "^1.11.10", "zustand": "^4.4.7", "immer": "^10.0.3", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-beautiful-dnd": "^13.1.1", "@types/react-beautiful-dnd": "^13.1.8", "react-virtualized": "^9.22.5", "@types/react-virtualized": "^9.21.29", "file-saver": "^2.0.5", "@types/file-saver": "^2.0.7", "xlsx": "^0.18.5", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "react-hotkeys-hook": "^4.4.1", "react-use": "^17.4.2"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "c8": "^8.0.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "jsdom": "^23.0.1", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/react": "^7.6.6", "@storybook/react-vite": "^7.6.6", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "@types/three": "^0.158.3", "sass": "^1.69.5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
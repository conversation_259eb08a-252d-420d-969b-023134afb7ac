import React, { useRef, useEffect, useState } from 'react'
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from '@react-three/fiber'
import { OrbitControls, Grid, Box, Text } from '@react-three/drei'
import { Card, Button, Space, Slider, Switch, Select, Tooltip } from 'antd'
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  SettingOutlined,
} from '@ant-design/icons'
import * as THREE from 'three'

import { Facility, Section, Aisle, Bay, Level, Location } from '@/types/facility'
import { useTheme } from '@/hooks/useTheme'

const { Option } = Select

interface FacilityVisualizationProps {
  facility: Facility
  selectedLocation?: Location
  onLocationSelect?: (location: Location) => void
  showProducts?: boolean
  showHeatmap?: boolean
}

/**
 * 3D Facility Visualization Component
 * 
 * Replaces the C++ AutoCAD integration and 2D facility drawing functionality.
 * Provides interactive 3D visualization of warehouse facilities.
 * 
 * Original C++ equivalent: Client/arx_modal/FacilityTools.cpp, AutoCAD drawing functions
 * 
 * Key improvements over C++:
 * - Web-based 3D visualization using Three.js
 * - Interactive navigation and selection
 * - Real-time data visualization
 * - Responsive design for all devices
 * - No dependency on AutoCAD software
 * - Heat map visualization for optimization results
 */

// 3D Section Component
const SectionMesh: React.FC<{
  section: Section
  onSelect: (section: Section) => void
  selected: boolean
}> = ({ section, onSelect, selected }) => {
  const meshRef = useRef<THREE.Mesh>(null)
  const { isDark } = useTheme()

  const handleClick = (event: any) => {
    event.stopPropagation()
    onSelect(section)
  }

  return (
    <group position={[section.coordinates?.xCoord || 0, 0, section.coordinates?.yCoord || 0]}>
      <Box
        ref={meshRef}
        args={[10, 1, 8]}
        onClick={handleClick}
        onPointerOver={(e) => {
          e.stopPropagation()
          document.body.style.cursor = 'pointer'
        }}
        onPointerOut={() => {
          document.body.style.cursor = 'auto'
        }}
      >
        <meshStandardMaterial
          color={selected ? '#1890ff' : isDark ? '#434343' : '#f0f0f0'}
          transparent
          opacity={0.8}
        />
      </Box>
      
      {/* Section Label */}
      <Text
        position={[0, 2, 0]}
        fontSize={0.8}
        color={isDark ? '#ffffff' : '#000000'}
        anchorX="center"
        anchorY="middle"
      >
        {section.name}
      </Text>
      
      {/* Render Aisles */}
      {section.aisles?.map((aisle, index) => (
        <AisleMesh
          key={aisle.id}
          aisle={aisle}
          position={[index * 2 - (section.aisles!.length - 1), 0, 0]}
        />
      ))}
    </group>
  )
}

// 3D Aisle Component
const AisleMesh: React.FC<{
  aisle: Aisle
  position: [number, number, number]
}> = ({ aisle, position }) => {
  const { isDark } = useTheme()

  return (
    <group position={position}>
      {/* Aisle Structure */}
      <Box args={[1.5, 0.5, 6]}>
        <meshStandardMaterial
          color={isDark ? '#595959' : '#d9d9d9'}
          transparent
          opacity={0.6}
        />
      </Box>
      
      {/* Aisle Label */}
      <Text
        position={[0, 1, 0]}
        fontSize={0.4}
        color={isDark ? '#ffffff' : '#000000'}
        anchorX="center"
        anchorY="middle"
      >
        {aisle.name}
      </Text>
      
      {/* Render Bays */}
      {aisle.bays?.map((bay, index) => (
        <BayMesh
          key={bay.id}
          bay={bay}
          position={[0, 0, index * 1.2 - (aisle.bays!.length - 1) * 0.6]}
        />
      ))}
    </group>
  )
}

// 3D Bay Component
const BayMesh: React.FC<{
  bay: Bay
  position: [number, number, number]
}> = ({ bay, position }) => {
  const { isDark } = useTheme()

  return (
    <group position={position}>
      {/* Bay Structure */}
      <Box args={[1.2, 0.3, 0.8]}>
        <meshStandardMaterial
          color={isDark ? '#722ed1' : '#b37feb'}
          transparent
          opacity={0.7}
        />
      </Box>
      
      {/* Render Levels */}
      {bay.levels?.map((level, index) => (
        <LevelMesh
          key={level.id}
          level={level}
          position={[0, index * 0.4 + 0.2, 0]}
        />
      ))}
    </group>
  )
}

// 3D Level Component
const LevelMesh: React.FC<{
  level: Level
  position: [number, number, number]
}> = ({ level, position }) => {
  const { isDark } = useTheme()

  return (
    <group position={position}>
      {/* Level Platform */}
      <Box args={[1.0, 0.1, 0.6]}>
        <meshStandardMaterial
          color={isDark ? '#13c2c2' : '#87e8de'}
          transparent
          opacity={0.8}
        />
      </Box>
      
      {/* Render Locations */}
      {level.locations?.map((location, index) => (
        <LocationMesh
          key={location.id}
          location={location}
          position={[index * 0.2 - (level.locations!.length - 1) * 0.1, 0.1, 0]}
        />
      ))}
    </group>
  )
}

// 3D Location Component
const LocationMesh: React.FC<{
  location: Location
  position: [number, number, number]
}> = ({ location, position }) => {
  const { isDark } = useTheme()
  
  // Color based on location status
  const getLocationColor = () => {
    switch (location.status) {
      case 'AVAILABLE':
        return '#52c41a'
      case 'OCCUPIED':
        return '#faad14'
      case 'RESERVED':
        return '#1890ff'
      case 'BLOCKED':
        return '#ff4d4f'
      default:
        return isDark ? '#8c8c8c' : '#d9d9d9'
    }
  }

  return (
    <group position={position}>
      <Box args={[0.15, 0.05, 0.15]}>
        <meshStandardMaterial
          color={getLocationColor()}
          transparent
          opacity={0.9}
        />
      </Box>
    </group>
  )
}

// Camera Controls Component
const CameraController: React.FC<{ facility: Facility }> = ({ facility }) => {
  const { camera } = useThree()
  
  useEffect(() => {
    // Set initial camera position based on facility size
    const facilitySize = Math.max(
      facility.dimensions?.width || 50,
      facility.dimensions?.depth || 50
    )
    camera.position.set(facilitySize * 0.8, facilitySize * 0.6, facilitySize * 0.8)
    camera.lookAt(0, 0, 0)
  }, [camera, facility])

  return null
}

// Main Visualization Component
const FacilityVisualization: React.FC<FacilityVisualizationProps> = ({
  facility,
  selectedLocation,
  onLocationSelect,
  showProducts = false,
  showHeatmap = false,
}) => {
  const [selectedSection, setSelectedSection] = useState<Section | null>(null)
  const [viewMode, setViewMode] = useState<'3d' | '2d'>('3d')
  const [zoom, setZoom] = useState(1)
  const [showGrid, setShowGrid] = useState(true)
  const [showLabels, setShowLabels] = useState(true)
  const { isDark } = useTheme()

  const handleSectionSelect = (section: Section) => {
    setSelectedSection(section)
  }

  const handleReset = () => {
    setSelectedSection(null)
    setZoom(1)
  }

  const handleFullscreen = () => {
    // TODO: Implement fullscreen mode
  }

  return (
    <Card
      title="设施可视化"
      extra={
        <Space>
          <Select value={viewMode} onChange={setViewMode} style={{ width: 80 }}>
            <Option value="3d">3D</Option>
            <Option value="2d">2D</Option>
          </Select>
          <Tooltip title="重置视图">
            <Button icon={<ReloadOutlined />} onClick={handleReset} />
          </Tooltip>
          <Tooltip title="全屏">
            <Button icon={<FullscreenOutlined />} onClick={handleFullscreen} />
          </Tooltip>
        </Space>
      }
    >
      {/* Controls Panel */}
      <div style={{ marginBottom: 16 }}>
        <Space wrap>
          <span>缩放:</span>
          <Slider
            style={{ width: 100 }}
            min={0.5}
            max={3}
            step={0.1}
            value={zoom}
            onChange={setZoom}
          />
          <Switch
            checked={showGrid}
            onChange={setShowGrid}
            checkedChildren="网格"
            unCheckedChildren="网格"
          />
          <Switch
            checked={showLabels}
            onChange={setShowLabels}
            checkedChildren="标签"
            unCheckedChildren="标签"
          />
          <Switch
            checked={showProducts}
            onChange={() => {}}
            checkedChildren="产品"
            unCheckedChildren="产品"
          />
          <Switch
            checked={showHeatmap}
            onChange={() => {}}
            checkedChildren="热力图"
            unCheckedChildren="热力图"
          />
        </Space>
      </div>

      {/* 3D Canvas */}
      <div style={{ height: 600, border: '1px solid #d9d9d9', borderRadius: 6 }}>
        <Canvas
          camera={{ position: [30, 20, 30], fov: 60 }}
          style={{ background: isDark ? '#141414' : '#f5f5f5' }}
        >
          {/* Lighting */}
          <ambientLight intensity={0.6} />
          <directionalLight position={[10, 10, 5]} intensity={0.8} />
          <pointLight position={[-10, -10, -5]} intensity={0.4} />

          {/* Grid */}
          {showGrid && (
            <Grid
              args={[100, 100]}
              cellSize={1}
              cellThickness={0.5}
              cellColor={isDark ? '#434343' : '#e8e8e8'}
              sectionSize={10}
              sectionThickness={1}
              sectionColor={isDark ? '#595959' : '#d9d9d9'}
            />
          )}

          {/* Facility Sections */}
          {facility.sections?.map((section) => (
            <SectionMesh
              key={section.id}
              section={section}
              onSelect={handleSectionSelect}
              selected={selectedSection?.id === section.id}
            />
          ))}

          {/* Camera Controller */}
          <CameraController facility={facility} />

          {/* Controls */}
          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            maxPolarAngle={Math.PI / 2}
            minDistance={5}
            maxDistance={200}
          />
        </Canvas>
      </div>

      {/* Selection Info */}
      {selectedSection && (
        <div style={{ marginTop: 16, padding: 16, background: '#f5f5f5', borderRadius: 6 }}>
          <h4>选中区域: {selectedSection.name}</h4>
          <p>类型: {selectedSection.sectionType}</p>
          <p>通道数: {selectedSection.aisles?.length || 0}</p>
          <p>状态: {selectedSection.status}</p>
        </div>
      )}
    </Card>
  )
}

export default FacilityVisualization

import React, { useState } from 'react'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import {
  Layout,
  Menu,
  Button,
  Avatar,
  Dropdown,
  Space,
  Typography,
  Breadcrumb,
  theme,
} from 'antd'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  BuildOutlined,
  <PERSON><PERSON>lotOutlined,
  <PERSON>boltOutlined,
  FileTextOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  BellOutlined,
} from '@ant-design/icons'

import { useAuth } from '@/hooks/useAuth'
import { useTheme } from '@/hooks/useTheme'
import NotificationCenter from '@/components/NotificationCenter'

const { Header, Sider, Content } = Layout
const { Title } = Typography

/**
 * Main Layout Component
 * 
 * Replaces the C++ MFC main window and menu system.
 * Provides the main application layout with navigation, header, and content areas.
 * 
 * Original C++ equivalent: Client/arx_modal main window framework
 * 
 * Key improvements over C++:
 * - Responsive design that works on all screen sizes
 * - Modern sidebar navigation with icons
 * - Breadcrumb navigation for better UX
 * - Theme switching support
 * - Real-time notifications
 * - User profile management
 */
const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout } = useAuth()
  const { isDark, toggleTheme } = useTheme()
  const {
    token: { colorBgContainer },
  } = theme.useToken()

  // Menu items configuration
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: '/facilities',
      icon: <BuildOutlined />,
      label: '设施管理',
    },
    {
      key: '/products',
      icon: <BoxPlotOutlined />,
      label: '产品管理',
    },
    {
      key: '/optimization',
      icon: <ThunderboltOutlined />,
      label: '优化管理',
    },
    {
      key: '/reports',
      icon: <FileTextOutlined />,
      label: '报表中心',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ]

  // Generate breadcrumb items based on current path
  const getBreadcrumbItems = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean)
    const breadcrumbItems = [
      {
        title: '首页',
        href: '/dashboard',
      },
    ]

    const pathMap: Record<string, string> = {
      dashboard: '仪表板',
      facilities: '设施管理',
      products: '产品管理',
      optimization: '优化管理',
      reports: '报表中心',
      settings: '系统设置',
    }

    pathSegments.forEach((segment, index) => {
      const path = '/' + pathSegments.slice(0, index + 1).join('/')
      const title = pathMap[segment] || segment
      
      breadcrumbItems.push({
        title,
        href: path,
      })
    })

    return breadcrumbItems
  }

  // User dropdown menu
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'theme',
      icon: <SettingOutlined />,
      label: isDark ? '浅色主题' : '深色主题',
      onClick: toggleTheme,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ]

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* Sidebar */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        theme={isDark ? 'dark' : 'light'}
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
        }}
      >
        {/* Logo */}
        <div
          style={{
            height: 64,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <Title
            level={4}
            style={{
              margin: 0,
              color: isDark ? '#fff' : '#1890ff',
              fontSize: collapsed ? 16 : 20,
            }}
          >
            {collapsed ? 'SLOT' : 'SLOT 系统'}
          </Title>
        </div>

        {/* Navigation Menu */}
        <Menu
          theme={isDark ? 'dark' : 'light'}
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ borderRight: 0 }}
        />
      </Sider>

      {/* Main Content Area */}
      <Layout style={{ marginLeft: collapsed ? 80 : 200, transition: 'margin-left 0.2s' }}>
        {/* Header */}
        <Header
          style={{
            padding: '0 24px',
            background: colorBgContainer,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: '1px solid #f0f0f0',
            position: 'sticky',
            top: 0,
            zIndex: 100,
          }}
        >
          {/* Left side - Collapse button and breadcrumb */}
          <Space size="large">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: '16px',
                width: 64,
                height: 64,
              }}
            />
            
            <Breadcrumb
              items={getBreadcrumbItems()}
              style={{ margin: '16px 0' }}
            />
          </Space>

          {/* Right side - Notifications and user menu */}
          <Space size="middle">
            <NotificationCenter />
            
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar
                  size="small"
                  icon={<UserOutlined />}
                  src={user?.avatar}
                />
                <span>{user?.name || '用户'}</span>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        {/* Content */}
        <Content
          style={{
            margin: '24px',
            padding: '24px',
            background: colorBgContainer,
            borderRadius: 8,
            minHeight: 'calc(100vh - 112px)',
            overflow: 'auto',
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  )
}

export default MainLayout

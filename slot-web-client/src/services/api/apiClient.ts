import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { message } from 'antd'

import { ApiResponse } from '@/types/api'
import { getToken, removeToken } from '@/utils/auth'

/**
 * API Client Service
 * 
 * Replaces the C++ client-server communication layer.
 * Provides a centralized HTTP client for all API communications.
 * 
 * Original C++ equivalent: Client/arx_modal/ConnectionManager.cpp, DataService.cpp
 * 
 * Key improvements over C++:
 * - Automatic request/response interceptors
 * - Token-based authentication
 * - Error handling and retry logic
 * - Request/response logging
 * - Type-safe API calls
 */

// API base configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'
const API_TIMEOUT = 30000 // 30 seconds

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add authentication token
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // Add request timestamp for debugging
    config.metadata = { startTime: new Date() }

    // Log request in development
    if (import.meta.env.DEV) {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
      })
    }

    return config
  },
  (error) => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Calculate request duration
    const duration = new Date().getTime() - response.config.metadata?.startTime?.getTime()

    // Log response in development
    if (import.meta.env.DEV) {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        duration: `${duration}ms`,
        data: response.data,
      })
    }

    // Handle API response wrapper
    if (response.data && typeof response.data === 'object' && 'success' in response.data) {
      const apiResponse = response.data as ApiResponse<any>
      
      if (!apiResponse.success) {
        // Handle API-level errors
        const errorMessage = apiResponse.error?.details || apiResponse.message || 'API请求失败'
        message.error(errorMessage)
        return Promise.reject(new Error(errorMessage))
      }
      
      // Return the actual data
      return {
        ...response,
        data: apiResponse.data,
      }
    }

    return response
  },
  (error) => {
    // Calculate request duration if available
    const duration = error.config?.metadata?.startTime 
      ? new Date().getTime() - error.config.metadata.startTime.getTime()
      : 0

    // Log error in development
    if (import.meta.env.DEV) {
      console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
        status: error.response?.status,
        duration: duration ? `${duration}ms` : 'unknown',
        error: error.response?.data || error.message,
      })
    }

    // Handle different error types
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          message.error('登录已过期，请重新登录')
          removeToken()
          window.location.href = '/login'
          break

        case 403:
          // Forbidden
          message.error('没有权限访问此资源')
          break

        case 404:
          // Not found
          message.error('请求的资源不存在')
          break

        case 422:
          // Validation error
          if (data?.error?.fieldErrors) {
            const fieldErrors = data.error.fieldErrors
            const errorMessages = fieldErrors.map((err: any) => `${err.field}: ${err.message}`)
            message.error(errorMessages.join(', '))
          } else {
            message.error(data?.message || '数据验证失败')
          }
          break

        case 429:
          // Too many requests
          message.error('请求过于频繁，请稍后再试')
          break

        case 500:
          // Internal server error
          message.error('服务器内部错误，请联系管理员')
          break

        case 502:
        case 503:
        case 504:
          // Server unavailable
          message.error('服务暂时不可用，请稍后再试')
          break

        default:
          // Other errors
          const errorMessage = data?.message || error.message || '请求失败'
          message.error(errorMessage)
      }
    } else if (error.request) {
      // Network error
      message.error('网络连接失败，请检查网络设置')
    } else {
      // Other errors
      message.error(error.message || '未知错误')
    }

    return Promise.reject(error)
  }
)

// Generic API methods
export const api = {
  // GET request
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    return apiClient.get(url, config).then(response => response.data)
  },

  // POST request
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return apiClient.post(url, data, config).then(response => response.data)
  },

  // PUT request
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return apiClient.put(url, data, config).then(response => response.data)
  },

  // PATCH request
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return apiClient.patch(url, data, config).then(response => response.data)
  },

  // DELETE request
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    return apiClient.delete(url, config).then(response => response.data)
  },

  // Upload file
  upload: <T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> => {
    const formData = new FormData()
    formData.append('file', file)

    return apiClient.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    }).then(response => response.data)
  },

  // Download file
  download: (url: string, filename?: string): Promise<void> => {
    return apiClient.get(url, {
      responseType: 'blob',
    }).then(response => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
  },
}

// Export the axios instance for advanced usage
export default apiClient

// Type declarations for metadata
declare module 'axios' {
  interface AxiosRequestConfig {
    metadata?: {
      startTime: Date
    }
  }
}

import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Tooltip,
  Modal,
  message,
  Popconfirm,
  Row,
  Col,
  Statistic,
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  ImportOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

import { useFacilities } from '@/hooks/useFacilities'
import { useAuth } from '@/hooks/useAuth'
import FacilityCreateModal from '@/components/facilities/FacilityCreateModal'
import FacilityImportModal from '@/components/facilities/FacilityImportModal'
import { Facility, FacilityStatus } from '@/types/facility'
import { formatDate } from '@/utils/date'

const { Search } = Input
const { Option } = Select

/**
 * Facilities Management Page
 * 
 * Replaces the C++ MFC facility management dialog and related functionality.
 * Provides a modern web interface for managing warehouse facilities.
 * 
 * Original C++ equivalent: Client/arx_modal/FacilityDialog.cpp
 * 
 * Key improvements over C++:
 * - Real-time data updates with React Query
 * - Advanced filtering and search capabilities
 * - Responsive table with pagination
 * - Bulk operations support
 * - Export/Import functionality
 * - Modern UI with Ant Design components
 */
const FacilitiesPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<FacilityStatus | 'all'>('all')
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [importModalVisible, setImportModalVisible] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])

  const {
    facilities,
    loading,
    error,
    pagination,
    setPagination,
    refetch,
    deleteFacility,
    bulkDeleteFacilities,
  } = useFacilities({
    search: searchText,
    status: statusFilter === 'all' ? undefined : statusFilter,
  })

  // Table columns configuration
  const columns: ColumnsType<Facility> = [
    {
      title: '设施名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      fixed: 'left',
      render: (text: string, record: Facility) => (
        <Button
          type="link"
          onClick={() => navigate(`/facilities/${record.id}`)}
          style={{ padding: 0, height: 'auto' }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '设施类型',
      dataIndex: 'facilityType',
      key: 'facilityType',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: FacilityStatus) => {
        const statusConfig = {
          ACTIVE: { color: 'green', text: '活跃' },
          INACTIVE: { color: 'red', text: '非活跃' },
          UNDER_CONSTRUCTION: { color: 'orange', text: '建设中' },
          MAINTENANCE: { color: 'blue', text: '维护中' },
          DECOMMISSIONED: { color: 'gray', text: '已停用' },
        }
        const config = statusConfig[status] || { color: 'default', text: status }
        return <Tag color={config.color}>{config.text}</Tag>
      },
    },
    {
      title: '尺寸 (长×宽×高)',
      key: 'dimensions',
      width: 150,
      render: (_, record: Facility) => {
        const { dimensions } = record
        if (!dimensions) return '-'
        return `${dimensions.length || 0} × ${dimensions.width || 0} × ${dimensions.height || 0}`
      },
    },
    {
      title: '区域数',
      dataIndex: 'sectionCount',
      key: 'sectionCount',
      width: 80,
      align: 'center',
    },
    {
      title: '产品数',
      dataIndex: 'productCount',
      key: 'productCount',
      width: 80,
      align: 'center',
    },
    {
      title: '位置数',
      dataIndex: 'locationCount',
      key: 'locationCount',
      width: 80,
      align: 'center',
    },
    {
      title: '集成状态',
      dataIndex: 'integrated',
      key: 'integrated',
      width: 100,
      align: 'center',
      render: (integrated: boolean) => (
        <Tag color={integrated ? 'green' : 'default'}>
          {integrated ? '已集成' : '未集成'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => formatDate(date),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record: Facility) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/facilities/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => navigate(`/facilities/${record.id}/edit`)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个设施吗？"
            description="删除后无法恢复，请谨慎操作。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  // Event handlers
  const handleSearch = (value: string) => {
    setSearchText(value)
    setPagination({ ...pagination, current: 1 })
  }

  const handleStatusFilterChange = (value: FacilityStatus | 'all') => {
    setStatusFilter(value)
    setPagination({ ...pagination, current: 1 })
  }

  const handleDelete = async (id: number) => {
    try {
      await deleteFacility(id)
      message.success('设施删除成功')
    } catch (error) {
      message.error('设施删除失败')
    }
  }

  const handleBulkDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的设施')
      return
    }

    Modal.confirm({
      title: '批量删除设施',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个设施吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await bulkDeleteFacilities(selectedRowKeys as number[])
          message.success('批量删除成功')
          setSelectedRowKeys([])
        } catch (error) {
          message.error('批量删除失败')
        }
      },
    })
  }

  const handleExport = () => {
    // TODO: Implement export functionality
    message.info('导出功能开发中...')
  }

  const handleTableChange = (newPagination: any) => {
    setPagination(newPagination)
  }

  // Calculate statistics
  const stats = {
    total: facilities?.length || 0,
    active: facilities?.filter(f => f.status === 'ACTIVE').length || 0,
    integrated: facilities?.filter(f => f.integrated).length || 0,
  }

  return (
    <div>
      {/* Page Header */}
      <div style={{ marginBottom: 24 }}>
        <h1>设施管理</h1>
        <p>管理仓储设施的基本信息、配置和状态</p>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic title="总设施数" value={stats.total} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="活跃设施" value={stats.active} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="已集成设施" value={stats.integrated} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="集成率"
              value={stats.total > 0 ? (stats.integrated / stats.total * 100).toFixed(1) : 0}
              suffix="%"
            />
          </Card>
        </Col>
      </Row>

      {/* Main Content Card */}
      <Card>
        {/* Toolbar */}
        <div style={{ marginBottom: 16 }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space>
                <Search
                  placeholder="搜索设施名称"
                  allowClear
                  style={{ width: 250 }}
                  onSearch={handleSearch}
                />
                <Select
                  value={statusFilter}
                  style={{ width: 120 }}
                  onChange={handleStatusFilterChange}
                >
                  <Option value="all">全部状态</Option>
                  <Option value="ACTIVE">活跃</Option>
                  <Option value="INACTIVE">非活跃</Option>
                  <Option value="UNDER_CONSTRUCTION">建设中</Option>
                  <Option value="MAINTENANCE">维护中</Option>
                  <Option value="DECOMMISSIONED">已停用</Option>
                </Select>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => refetch()}
                  loading={loading}
                >
                  刷新
                </Button>
              </Space>
            </Col>
            <Col>
              <Space>
                {selectedRowKeys.length > 0 && (
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                    onClick={handleBulkDelete}
                  >
                    批量删除 ({selectedRowKeys.length})
                  </Button>
                )}
                <Button
                  icon={<ExportOutlined />}
                  onClick={handleExport}
                >
                  导出
                </Button>
                <Button
                  icon={<ImportOutlined />}
                  onClick={() => setImportModalVisible(true)}
                >
                  导入
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setCreateModalVisible(true)}
                >
                  新建设施
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* Table */}
        <Table
          columns={columns}
          dataSource={facilities}
          loading={loading}
          rowKey="id"
          scroll={{ x: 1200 }}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            getCheckboxProps: (record: Facility) => ({
              disabled: record.status === 'DECOMMISSIONED',
            }),
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* Modals */}
      <FacilityCreateModal
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onSuccess={() => {
          setCreateModalVisible(false)
          refetch()
        }}
      />

      <FacilityImportModal
        visible={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        onSuccess={() => {
          setImportModalVisible(false)
          refetch()
        }}
      />
    </div>
  )
}

export default FacilitiesPage

import React, { useState } from 'react'
import {
  Card,
  Steps,
  Button,
  Form,
  Select,
  InputNumber,
  Switch,
  Space,
  Progress,
  Alert,
  Tabs,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
} from 'antd'
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SettingOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

import { useOptimization } from '@/hooks/useOptimization'
import { useFacilities } from '@/hooks/useFacilities'
import OptimizationResultsVisualization from '@/components/optimization/OptimizationResultsVisualization'
import { OptimizationJob, OptimizationResult, Pass1Options, Pass4Options } from '@/types/optimization'
import { formatDate, formatDuration } from '@/utils/date'

const { Step } = Steps
const { Option } = Select
const { TabPane } = Tabs

/**
 * Optimization Management Page
 * 
 * Replaces the C++ optimization execution and monitoring functionality.
 * Provides a modern web interface for running and monitoring SLOT optimization algorithms.
 * 
 * Original C++ equivalent: Client/arx_modal optimization dialogs and Pass managers
 * 
 * Key improvements over C++:
 * - Real-time optimization progress monitoring
 * - Interactive parameter configuration
 * - Visual results presentation
 * - Job queue management
 * - Historical optimization tracking
 * - Export and reporting capabilities
 */
const OptimizationPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const [selectedFacility, setSelectedFacility] = useState<number | null>(null)
  const [optimizationParams, setOptimizationParams] = useState<any>({})
  const [form] = Form.useForm()

  const { facilities } = useFacilities({ status: 'ACTIVE' })
  const {
    currentJob,
    jobHistory,
    startOptimization,
    pauseOptimization,
    stopOptimization,
    getOptimizationResults,
  } = useOptimization()

  // Optimization steps configuration
  const steps = [
    {
      title: '选择设施',
      description: '选择要优化的仓储设施',
    },
    {
      title: '配置参数',
      description: '设置优化算法参数',
    },
    {
      title: '执行优化',
      description: '运行优化算法',
    },
    {
      title: '查看结果',
      description: '分析优化结果',
    },
  ]

  // Job history table columns
  const jobColumns: ColumnsType<OptimizationJob> = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '设施',
      dataIndex: 'facilityName',
      key: 'facilityName',
      width: 150,
    },
    {
      title: '优化类型',
      dataIndex: 'optimizationType',
      key: 'optimizationType',
      width: 120,
      render: (type: string) => {
        const typeMap: Record<string, { text: string; color: string }> = {
          PASS1: { text: 'Pass1 货架选择', color: 'blue' },
          PASS3: { text: 'Pass3 区域分配', color: 'green' },
          PASS4: { text: 'Pass4 位置分配', color: 'orange' },
          PASS5: { text: 'Pass5 移动优化', color: 'purple' },
        }
        const config = typeMap[type] || { text: type, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap: Record<string, { text: string; color: string }> = {
          PENDING: { text: '等待中', color: 'default' },
          RUNNING: { text: '运行中', color: 'processing' },
          COMPLETED: { text: '已完成', color: 'success' },
          FAILED: { text: '失败', color: 'error' },
          CANCELLED: { text: '已取消', color: 'warning' },
        }
        const config = statusMap[status] || { text: status, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      },
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      ),
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 150,
      render: (date: string) => formatDate(date),
    },
    {
      title: '耗时',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (duration: number) => formatDuration(duration),
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record: OptimizationJob) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            onClick={() => handleViewResults(record.id)}
            disabled={record.status !== 'COMPLETED'}
          >
            查看结果
          </Button>
        </Space>
      ),
    },
  ]

  // Event handlers
  const handleFacilitySelect = (facilityId: number) => {
    setSelectedFacility(facilityId)
    setCurrentStep(1)
  }

  const handleParameterSubmit = (values: any) => {
    setOptimizationParams(values)
    setCurrentStep(2)
  }

  const handleStartOptimization = async () => {
    if (!selectedFacility) return

    try {
      await startOptimization({
        facilityId: selectedFacility,
        optimizationType: optimizationParams.optimizationType,
        parameters: optimizationParams,
      })
      setCurrentStep(3)
    } catch (error) {
      console.error('Failed to start optimization:', error)
    }
  }

  const handlePauseOptimization = async () => {
    if (currentJob) {
      await pauseOptimization(currentJob.id)
    }
  }

  const handleStopOptimization = async () => {
    if (currentJob) {
      await stopOptimization(currentJob.id)
    }
  }

  const handleViewResults = (jobId: number) => {
    // Navigate to results view
    setCurrentStep(3)
  }

  const handleReset = () => {
    setCurrentStep(0)
    setSelectedFacility(null)
    setOptimizationParams({})
    form.resetFields()
  }

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Card title="选择设施">
            <Row gutter={[16, 16]}>
              {facilities?.map((facility) => (
                <Col span={8} key={facility.id}>
                  <Card
                    hoverable
                    onClick={() => handleFacilitySelect(facility.id)}
                    style={{
                      border: selectedFacility === facility.id ? '2px solid #1890ff' : undefined,
                    }}
                  >
                    <Card.Meta
                      title={facility.name}
                      description={
                        <div>
                          <p>类型: {facility.facilityType}</p>
                          <p>产品数: {facility.productCount}</p>
                          <p>位置数: {facility.locationCount}</p>
                        </div>
                      }
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        )

      case 1:
        return (
          <Card title="配置优化参数">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleParameterSubmit}
              initialValues={{
                optimizationType: 'PASS1',
                regressionType: 'LINEAR',
                hotspotWeight: 0.8,
                considerMovement: true,
                considerCapacity: true,
              }}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="optimizationType"
                    label="优化类型"
                    rules={[{ required: true, message: '请选择优化类型' }]}
                  >
                    <Select>
                      <Option value="PASS1">Pass1 - 货架类型选择</Option>
                      <Option value="PASS3">Pass3 - 区域分配优化</Option>
                      <Option value="PASS4">Pass4 - 位置分配优化</Option>
                      <Option value="PASS5">Pass5 - 移动优化</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="regressionType"
                    label="回归类型"
                  >
                    <Select>
                      <Option value="LINEAR">线性回归</Option>
                      <Option value="POLYNOMIAL">多项式回归</Option>
                      <Option value="EXPONENTIAL">指数回归</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="hotspotWeight"
                    label="热点权重"
                    tooltip="热点区域的权重系数，影响优化结果"
                  >
                    <InputNumber
                      min={0}
                      max={1}
                      step={0.1}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="maxIterations"
                    label="最大迭代次数"
                  >
                    <InputNumber
                      min={1}
                      max={1000}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item
                    name="considerMovement"
                    label="考虑移动量"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="considerCapacity"
                    label="考虑容量限制"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="enableParallelProcessing"
                    label="并行处理"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Space>
                  <Button onClick={() => setCurrentStep(0)}>上一步</Button>
                  <Button type="primary" htmlType="submit">
                    下一步
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        )

      case 2:
        return (
          <Card title="执行优化">
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              {/* Current Job Status */}
              {currentJob && (
                <Alert
                  message={`正在执行 ${currentJob.optimizationType} 优化`}
                  description={`设施: ${currentJob.facilityName} | 状态: ${currentJob.status}`}
                  type="info"
                  showIcon
                />
              )}

              {/* Progress */}
              {currentJob && (
                <div>
                  <Progress
                    percent={currentJob.progress}
                    status={currentJob.status === 'FAILED' ? 'exception' : 'active'}
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                  />
                  <p style={{ marginTop: 8 }}>
                    预计剩余时间: {formatDuration(currentJob.estimatedTimeRemaining || 0)}
                  </p>
                </div>
              )}

              {/* Control Buttons */}
              <Space>
                {!currentJob || currentJob.status === 'COMPLETED' ? (
                  <Button
                    type="primary"
                    icon={<PlayCircleOutlined />}
                    onClick={handleStartOptimization}
                    size="large"
                  >
                    开始优化
                  </Button>
                ) : (
                  <>
                    <Button
                      icon={<PauseCircleOutlined />}
                      onClick={handlePauseOptimization}
                      disabled={currentJob.status !== 'RUNNING'}
                    >
                      暂停
                    </Button>
                    <Button
                      danger
                      icon={<StopOutlined />}
                      onClick={handleStopOptimization}
                      disabled={currentJob.status === 'COMPLETED'}
                    >
                      停止
                    </Button>
                  </>
                )}
                <Button onClick={() => setCurrentStep(1)}>
                  返回参数配置
                </Button>
                {currentJob?.status === 'COMPLETED' && (
                  <Button
                    type="primary"
                    onClick={() => setCurrentStep(3)}
                  >
                    查看结果
                  </Button>
                )}
              </Space>
            </Space>
          </Card>
        )

      case 3:
        return (
          <Card title="优化结果">
            {currentJob?.results ? (
              <OptimizationResultsVisualization
                results={currentJob.results}
                facilityId={selectedFacility!}
              />
            ) : (
              <Alert
                message="暂无结果"
                description="请先执行优化算法"
                type="warning"
                showIcon
              />
            )}
          </Card>
        )

      default:
        return null
    }
  }

  return (
    <div>
      {/* Page Header */}
      <div style={{ marginBottom: 24 }}>
        <h1>优化管理</h1>
        <p>执行和监控SLOT仓储优化算法</p>
      </div>

      {/* Main Content */}
      <Row gutter={24}>
        <Col span={18}>
          {/* Steps */}
          <Card style={{ marginBottom: 24 }}>
            <Steps current={currentStep} items={steps} />
          </Card>

          {/* Step Content */}
          {renderStepContent()}
        </Col>

        <Col span={6}>
          {/* Quick Actions */}
          <Card title="快速操作" style={{ marginBottom: 24 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                block
                icon={<ReloadOutlined />}
                onClick={handleReset}
              >
                重新开始
              </Button>
              <Button
                block
                icon={<DownloadOutlined />}
                disabled={!currentJob?.results}
              >
                导出结果
              </Button>
              <Button
                block
                icon={<SettingOutlined />}
              >
                高级设置
              </Button>
            </Space>
          </Card>

          {/* Statistics */}
          <Card title="统计信息">
            <Statistic
              title="历史任务数"
              value={jobHistory?.length || 0}
              style={{ marginBottom: 16 }}
            />
            <Statistic
              title="成功率"
              value={
                jobHistory?.length
                  ? ((jobHistory.filter(job => job.status === 'COMPLETED').length / jobHistory.length) * 100).toFixed(1)
                  : 0
              }
              suffix="%"
            />
          </Card>
        </Col>
      </Row>

      {/* Job History */}
      <Card title="优化历史" style={{ marginTop: 24 }}>
        <Table
          columns={jobColumns}
          dataSource={jobHistory}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Card>
    </div>
  )
}

export default OptimizationPage

/**
 * Facility Type Definitions
 * 
 * TypeScript type definitions for facility-related data structures.
 * Replaces C++ header files and provides type safety for the web client.
 * 
 * Original C++ equivalent: Server/SLOTFacility.h, SLOTSection.h, etc.
 * 
 * Key improvements over C++:
 * - Type safety at compile time
 * - IntelliSense support in IDEs
 * - Automatic validation
 * - Better documentation through types
 */

// Base entity interface
export interface BaseEntity {
  id: number
  description?: string
  createdAt: string
  updatedAt: string
  deleted?: boolean
}

// Facility status enumeration
export type FacilityStatus = 
  | 'ACTIVE'
  | 'INACTIVE' 
  | 'UNDER_CONSTRUCTION'
  | 'MAINTENANCE'
  | 'DECOMMISSIONED'

// Facility dimensions
export interface FacilityDimensions {
  width?: number
  height?: number
  depth?: number
  area?: number
  volume?: number
}

// Address information
export interface Address {
  streetAddress?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
}

// Facility interface
export interface Facility extends BaseEntity {
  name: string
  facilityType?: string
  status: FacilityStatus
  dimensions?: FacilityDimensions
  address?: Address
  cadFilePath?: string
  maxNum?: number
  integrated: boolean
  
  // Relationships
  sections?: Section[]
  products?: ProductPack[]
  hotspots?: Hotspot[]
  
  // Computed fields
  sectionCount?: number
  productCount?: number
  locationCount?: number
}

// Section status
export type SectionStatus = 
  | 'ACTIVE'
  | 'INACTIVE'
  | 'UNDER_CONSTRUCTION'
  | 'MAINTENANCE'

// Coordinates
export interface Coordinates {
  xCoord?: number
  yCoord?: number
  zCoord?: number
  rotation?: number
}

// Section interface
export interface Section extends BaseEntity {
  name: string
  sectionCode?: string
  facilityId: number
  coordinates?: Coordinates
  sectionType?: string
  status: SectionStatus
  
  // Relationships
  facility?: Facility
  aisles?: Aisle[]
  hotspots?: Hotspot[]
}

// Aisle status
export type AisleStatus = 
  | 'ACTIVE'
  | 'INACTIVE'
  | 'UNDER_CONSTRUCTION'
  | 'MAINTENANCE'
  | 'BLOCKED'

// Aisle dimensions
export interface AisleDimensions {
  length?: number
  width?: number
  height?: number
  xCoord?: number
  yCoord?: number
  zCoord?: number
  rotation?: number
}

// Entry/Exit points
export interface EntryExitPoints {
  entryX?: number
  entryY?: number
  exitX?: number
  exitY?: number
}

// Aisle interface
export interface Aisle extends BaseEntity {
  name: string
  aisleCode?: string
  sectionId: number
  dimensions?: AisleDimensions
  aisleProfileId?: number
  entryExitPoints?: EntryExitPoints
  status: AisleStatus
  
  // Relationships
  section?: Section
  aisleProfile?: AisleProfile
  bays?: Bay[]
}

// Aisle profile interface
export interface AisleProfile extends BaseEntity {
  name: string
  profileType: string
  defaultWidth?: number
  defaultHeight?: number
  rackingType?: string
  
  // Relationships
  aisles?: Aisle[]
}

// Bay interface
export interface Bay extends BaseEntity {
  name: string
  bayCode?: string
  aisleId: number
  bayNumber?: number
  status: string
  
  // Relationships
  aisle?: Aisle
  levels?: Level[]
}

// Level interface
export interface Level extends BaseEntity {
  name: string
  levelCode?: string
  bayId: number
  levelNumber?: number
  height?: number
  status: string
  
  // Relationships
  bay?: Bay
  locations?: Location[]
}

// Location status
export type LocationStatus = 
  | 'AVAILABLE'
  | 'OCCUPIED'
  | 'RESERVED'
  | 'BLOCKED'
  | 'MAINTENANCE'

// Location interface
export interface Location extends BaseEntity {
  name: string
  locationCode?: string
  levelId: number
  positionNumber?: number
  status: LocationStatus
  capacity?: number
  currentOccupancy?: number
  
  // Physical properties
  width?: number
  height?: number
  depth?: number
  weight?: number
  
  // Relationships
  level?: Level
  currentProduct?: ProductPack
  assignments?: Assignment[]
}

// Product status
export type ProductStatus = 
  | 'ACTIVE'
  | 'INACTIVE'
  | 'DISCONTINUED'
  | 'PENDING_APPROVAL'

// Product dimensions
export interface ProductDimensions {
  width?: number
  height?: number
  depth?: number
}

// Cube calculations
export interface CubeCalculations {
  basicCube?: number
  extendedCube?: number
  inventoryCube?: number
  caseCube?: number
}

// Product pack interface
export interface ProductPack extends BaseEntity {
  sku: string
  productDescription?: string
  wmsId?: string
  wmsDetailId?: string
  movement: number
  balanceOnHand: number
  unitOfIssue: number
  weight?: number
  hazardFlag: boolean
  dimensions?: ProductDimensions
  cubeCalculations?: CubeCalculations
  facilityId: number
  currentLocationId?: number
  category?: string
  status: ProductStatus
  
  // Relationships
  facility?: Facility
  currentLocation?: Location
}

// Hotspot interface
export interface Hotspot extends BaseEntity {
  name: string
  facilityId: number
  sectionId?: number
  coordinates: Coordinates
  hotspotType: string
  weight: number
  radius?: number
  
  // Relationships
  facility?: Facility
  section?: Section
}

// Assignment interface
export interface Assignment extends BaseEntity {
  productPackId: number
  locationId: number
  solutionId: number
  assignmentType: string
  priority?: number
  quantity?: number
  
  // Relationships
  productPack?: ProductPack
  location?: Location
  solution?: Solution
}

// Solution interface
export interface Solution extends BaseEntity {
  facilityId: number
  optimizationType: string
  status: string
  parameters?: Record<string, any>
  results?: Record<string, any>
  
  // Relationships
  facility?: Facility
  assignments?: Assignment[]
}

// Request/Response DTOs
export interface FacilityCreateRequest {
  name: string
  description?: string
  facilityType?: string
  status?: FacilityStatus
  dimensions?: FacilityDimensions
  address?: Address
  cadFilePath?: string
  integrated?: boolean
}

export interface FacilityUpdateRequest extends Partial<FacilityCreateRequest> {
  id: number
}

export interface FacilityResponse extends Facility {
  // Additional computed fields for response
  totalLocations?: number
  totalProducts?: number
  utilizationRate?: number
}

export interface FacilitySummaryResponse {
  facility: FacilityResponse
  totalLocations: number
  totalProducts: number
  totalSections: number
  utilizationStats?: {
    occupiedLocations: number
    availableLocations: number
    utilizationRate: number
  }
}

// Search and filter interfaces
export interface FacilitySearchParams {
  name?: string
  status?: FacilityStatus
  facilityType?: string
  integrated?: boolean
  page?: number
  size?: number
  sort?: string
  direction?: 'asc' | 'desc'
}

export interface FacilityListResponse {
  content: FacilityResponse[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}

// Validation schemas (for use with form validation)
export interface FacilityValidationRules {
  name: {
    required: boolean
    maxLength: number
  }
  facilityType: {
    maxLength: number
  }
  dimensions: {
    width: { min: number; max: number }
    height: { min: number; max: number }
    depth: { min: number; max: number }
  }
}

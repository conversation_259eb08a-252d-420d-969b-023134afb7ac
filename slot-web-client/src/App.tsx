import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from 'react-query'
import { ReactQueryDevtools } from 'react-query/devtools'
import { ConfigProvider, App as AntdApp } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

import { AuthProvider } from '@/contexts/AuthContext'
import { ThemeProvider } from '@/contexts/ThemeContext'
import { NotificationProvider } from '@/contexts/NotificationContext'
import Layout from '@/components/Layout'
import ProtectedRoute from '@/components/ProtectedRoute'
import ErrorBoundary from '@/components/ErrorBoundary'

// Pages
import LoginPage from '@/pages/auth/LoginPage'
import DashboardPage from '@/pages/dashboard/DashboardPage'
import FacilitiesPage from '@/pages/facilities/FacilitiesPage'
import FacilityDetailPage from '@/pages/facilities/FacilityDetailPage'
import ProductsPage from '@/pages/products/ProductsPage'
import OptimizationPage from '@/pages/optimization/OptimizationPage'
import ReportsPage from '@/pages/reports/ReportsPage'
import SettingsPage from '@/pages/settings/SettingsPage'
import NotFoundPage from '@/pages/error/NotFoundPage'

import '@/styles/global.scss'

// Set dayjs locale
dayjs.locale('zh-cn')

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
    mutations: {
      retry: 1,
    },
  },
})

/**
 * Main Application Component
 * 
 * Replaces the C++ MFC application framework with modern React architecture.
 * 
 * Original C++ equivalent: Client/arx_modal/Startup.cpp (main application entry)
 * 
 * Key improvements over C++:
 * - Modern React component architecture
 * - Declarative routing with React Router
 * - Global state management with Context API
 * - Internationalization support
 * - Error boundaries for better error handling
 * - Hot module replacement for development
 */
const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider
          locale={zhCN}
          theme={{
            token: {
              colorPrimary: '#1890ff',
              borderRadius: 6,
            },
          }}
        >
          <AntdApp>
            <ThemeProvider>
              <NotificationProvider>
                <AuthProvider>
                  <Router>
                    <Routes>
                      {/* Public Routes */}
                      <Route path="/login" element={<LoginPage />} />
                      
                      {/* Protected Routes */}
                      <Route
                        path="/"
                        element={
                          <ProtectedRoute>
                            <Layout />
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<Navigate to="/dashboard" replace />} />
                        <Route path="dashboard" element={<DashboardPage />} />
                        
                        {/* Facility Management */}
                        <Route path="facilities" element={<FacilitiesPage />} />
                        <Route path="facilities/:id" element={<FacilityDetailPage />} />
                        
                        {/* Product Management */}
                        <Route path="products" element={<ProductsPage />} />
                        
                        {/* Optimization */}
                        <Route path="optimization" element={<OptimizationPage />} />
                        
                        {/* Reports */}
                        <Route path="reports" element={<ReportsPage />} />
                        
                        {/* Settings */}
                        <Route path="settings" element={<SettingsPage />} />
                      </Route>
                      
                      {/* 404 Page */}
                      <Route path="*" element={<NotFoundPage />} />
                    </Routes>
                  </Router>
                </AuthProvider>
              </NotificationProvider>
            </ThemeProvider>
          </AntdApp>
        </ConfigProvider>
        
        {/* Development tools */}
        {process.env.NODE_ENV === 'development' && (
          <ReactQueryDevtools initialIsOpen={false} />
        )}
      </QueryClientProvider>
    </ErrorBoundary>
  )
}

export default App

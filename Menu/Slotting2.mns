//
//      AutoCAD menu file - C:\Program Files\AutoCAD R14\support\Slotting2.mns
//

***MENUGROUP=SLOTTING2

***BUTTONS1
**BAYPROP
$P0=SLOTTING2.POP0 $p0=*
^C^C

***AUX1
**BAYPROP
$P0=SLOTTING2.POP0 $p0=*
^C^C 
***POP0
**BAYCURSORMN
                 [Optimize Right click properties cursor menu]
M_WIZARD				[Main &Wizard]^C^CSHOWWIZARD
               [--]
M_POP_INSERT				[->Insert]
M_POP_ADD_AISLE				[&Aisle]^C^CADDAISLE
M_POP_ADD_PICKPATH			[&Pick Path]^C^CADDPICKPATH
M_POP_ADD_HOTSPOT			[<-&Hotspot]^C^CADDHOTSPOT
M_POP_REMOVE				[->Delete]
M_POP_DELETE_BAY 				[&Bay]^C^CDELETEBAY
M_POP_DELETE_AISLE 				[&Aisle]^C^CDELETEAISLE
M_POP_DELETE_PICKPATH 			[<-&Pick Path]^C^CDELETEPICKPATH
M_POP_LEVELLOC_PROPERTIES 	[P&roperties]^C^CLEVELLOCHOLDER
M_POP_FACILITY_EXPLORER		[Facility &Explorer]^C^CFACILITYTOOLS
               [--]
M_POP_CHANGE_BAYPROFILE		[&Change Bay Profile]^C^CCHANGERACKTYPE
               [--]
M_POP_WIZARDS				[->Create &Profile]
M_POP_BAYWIZARD					[&Bay Profile]^C^CBAYWIZARD
M_POP_SIDEWIZARD				[&Side Profile]^C^CSIDEWIZARD
M_POP_AISLEWIZARD				[<-&Aisle Profile]^C^CAISLEWIZARD
			[--]
M_POP_FACILITY				[->&Facility]
M_POP_NEW_FACILITY				[&New]^C^CNEWFACILITY
M_POP_OPEN_FACILITY				[&Open]^C^COPENFACILITY
M_POP_SAVE_FACILITY				[&Save]^C^CFACSAVE
M_POP_CHECK_FACILITY			[<-&Check]^C^CCLEANFACILITY

***POP1
**WAREHOUSE
M_FACILITY			[Fa&cility]
M_NEW_FACILITY     		[&New Facility]^C^CNEWFACILITY 
M_OPEN_FACILITY			[&Open Facility]^C^COPENFACILITY
M_DELETE_FACILITY		[&Delete Facility]^C^CFACDELETE
M_SAVE_FACILITY			[&Save Facility]^C^CFACSAVE
M_SAVEAS_FACILITY		[Save &As Facility]^C^CFACSAVEAS
			[--]
M_WIZARDS				[->Create &Profile]
M_BAYWIZARD					[&Bay Profile]^C^CBAYWIZARD
M_SIDEWIZARD				[&Side Profile]^C^CSIDEWIZARD
M_AISLEWIZARD				[<-&Aisle Profile]^C^CAISLEWIZARD
			[--]
M_INSERT				[->&Insert]
M_ADD_AISLE					[&Aisle]^C^CADDAISLE 
M_ADD_PICKPATH				[&Pick Path]^C^CADDPICKPATH 
M_ADD_HOTSPOT				[<-&Hot Spot]^C^CADDHOTSPOT
M_REMOVE				[->&Remove]
M_DELETE_AISLE				[&Aisle]^C^CDeleteAisle
M_DELETE_BAY				[&Bay]^C^CDeleteBay
M_DELETE_PICKPATH			[<-&Pick Path]^C^CDeletePickPath
M_CHANGE_BAYPROFILE		[&Change Bay Profile]^C^CChangeRackType
			[--]
M_MAINTAIN				[->&Maintain Facility]
M_FACILITY_EXPLORER			[Facility &Explorer]^C^CFACILITYTOOLS
M_PROPERTIES				[->Element &Properties]
M_FACILITY_PROPERTIES			[&Facility]^C^CFACILITYHOLDER
M_SECTION_PROPERTIES			[&Section]^C^CSECTIONHOLDER
M_AISLE_PROPERTIES				[&Aisle]^C^CAISLEHOLDER
M_SIDE_PROPERTIES				[S&ide]^C^CSIDEHOLDER
M_BAY_PROPERTIES				[&Bay]^C^CBAYHOLDER
M_LEVELLOC_PROPERTIES			[<-<-&Level/Location]^C^CLEVELLOCHOLDER
			[--]
M_UTILITY				[->&Utility]
M_USER_QUERY				[&Query Library]^C^CUSERQUERY
M_CHECK_FACILITY			[<-&Check Facility]^C^CCLEANFACILITY
M_EXPORT_DATABASE			[&Export Database]^C^CEXPORTDATABASE
			[--]
M_NEW_CONNECTION      	[Ne&w Database Connection]^C^CNEWCONNECTION2
M_WIZARD				[Op&timize Wizard]^C^CSHOWWIZARD
M_HELP					[Optimize &Help]^C^CSUCCEEDHELP
               [--]
M_CLOSE					[C&lose]^C^C_QUIT


***POP3
**PASSES
M_OPTIMIZATION			[O&ptimization]
M_INTERFACES  			[->Inter&faces]
M_PRODUCT_INBOUND  			[Product Inbound]^C^CPRODINBOUND
M_LOCATION_OUTBOUND  		[Location Outbound]^C^CLOCATIONOUTBOUND
M_SOLUTION_OUTBOUND  		[Assignment Outbound]^C^CASSIGNMENTOUTBOUND
M_GENERATE_MOVES 			[Optimize Moves Outbound]^C^CGENERATEMOVES
               [--]
M_WMS_SETUP  				[Configure Integration]^C^CWMSSETUP
M_SEARCH_ANCHOR_MAINTENANCE [<-&Define Search Anchor Points]^C^CSEARCHANCHORMAINTENANCE
               [--]
M_PRODUCTS				[->&Products]
M_PRODUCT_MAINTENANCE 		[Pr&oduct Maintenance]^C^CPRODUCTMAINTENANCE
M_DELETE_PRODUCTS_BY_FAC	[&Delete All Products In Facility]^C^CDELETEPRODUCTSBYFACILITY
M_DATA_MODELER  			[Data &Modeler]^C^CDATAMODELER 
M_DATA_PURIFICATION  		[<-Data &Purification]^C^CDATAPURIFICATION 
M_PRODUCT_GROUPS			[->Product &Groups]
M_PRODUCT_GROUP_MAINTENANCE	[&Define Product Groups]^C^CPRODUCTGROUPMAINTENANCE 
M_ASSIGN_PRODUCT_GROUP 		[&Assign Product Groups Manually]^C^CASSIGNSLOTGROUP
M_UNASSIGN_PRODUCT_GROUP 	[<-&Unassign Product Groups Manually]^C^CUNASSIGNSLOTGROUP
M_UDF_MAINTENANCE 		[&UDF Maintenance]^C^CUDFMAINTENANCE 
               [--]
M_OPTIMIZE			[->&Optimize]
M_CAPITAL_COST 				[&Calculate Capital Cost]^C^CRACKASSIGN 
M_PRODUCT_GROUP_LAYOUT 		[Layout Product &Groups]^C^CSLOTGROUP 
M_PRODUCT_LAYOUT 			[&Layout Product]^C^CPRODUCTLOC 
M_COST_COMPARISON 			[<-C&ompare Costs]^C^CCOSTCOMPARISON
               [--]
M_COLORING				[->&Coloring]
M_COLOR_MODEL   			[Color &Model]^C^CCOLORMODEL
M_COLOR_PRODUCT_GROUP 		[Color Product &Groups]^C^CCOLORSLOTGROUP
M_COLOR_AISLE  				[Color &Aisle]^C^CCOLORAISLE
M_COLOR_BY_PROFILE 			[Color &Bay Profile]^C^CCOLORBYPROFILE
M_COLOR_BY_PRODUCT 			[<-Color &Product Assignments]^C^CCOLORBYPRODUCT

***POP4
**REPORTS
M_REPORTS       					[Reports]
M_OPEN_SAVED_REPORT						[Open Saved Report]^C^COPENSAVEDREP
M_CAPITAL_COST_REP						[->Capital Cost]
M_CAPITAL_COST_SUMMARY_REP					[Capital Cost Summary]^C^CRACKASSIGNREP
M_CAPITAL_COST_DETAIL_REP					[Capital Cost Detail]^C^CRACKASSIGNDETREP
M_CAPITAL_COST_REJECTIONS_REP				[<-Capital Cost Rejections]^C^CCAPITALCOSTREJECTIONREP
M_PRODUCT_GROUPS_REP					[->Product Groups]
M_PRODUCT_GROUPS_BY_PRODUCT_REP				[Sorted By Product]^C^CPRODUCTGROUPREP
M_PRODUCT_GROUPS_BY_MOVEMENT_REP			[Sorted By Movement]^C^CPRODUCTGROUPBYMOVEMENTREP
M_PRODUCT_GROUPS_BY_BOH_REP					[Sorted By Balance on Hand]^C^CPRODUCTGROUPBYBOHREP
M_PRODUCT_GROUPS_BY_UOI_REP					[<-Sorted By Unit of Issue]^C^CPRODUCTGROUPBYUOIREP
M_PRODUCT_GROUP_LAYOUT_REP				[Product Group Layout]^C^CPRODGROUPLAYOUTREP
M_PRODUCT_LAYOUT_REP					[->Product Layout ]
M_PRODUCT_LAYOUT_FACINGS_REP				[Product Group Facings]^C^CPRODGROUPFACINGREP
M_PRODUCT_LAYOUT_ASSIGNMENTS_REP			[->Assignments]
M_PRODUCT_GROUP_LAYOUT_BY_PRODUCT_REP		[Sorted By Product]^C^CPRODGROUPLAYOUTBYPRODUCTREP
M_PRODUCT_GROUP_LAYOUT_BY_PRODUCT_REP		[<-Sorted By Location]^C^CPRODGROUPLAYOUTBYLOCATIONREP
M_PRODUCT_LAYOUT_VARIABLE_WIDTH_REP			[Variable Width Location]^C^CPRODGROUPLAYOUTLOCREP
M_PRODUCT_LAYOUT_CASE_REORIENTATION_REP		[<-Case Re-Orientation]^C^CPRODGROUPLAYOUTCASEREP
M_PRODUCT_MOVES_REP						[Product Moves]^C^CFACILITYMOVEREP
M_LOCATION_OUTBOUND_REP					[Location Outbound]^C^CLOCATIONOUTBOUNDREP
M_PRODUCT_DETAIL_REP					[Product Detail]^C^CPRODUCTDETAILREP
M_COST_ANALYSIS_REP						[->Cost Analysis]
M_COST_ANALYSIS_SUMMARY_REP					[Cost Analysis Summary]^C^CCOSTANALYSISSUMREP
M_COST_ANALYSIS_DETAIL_REP					[<-Cost Analysis Detail]^C^CCOSTANALYSISDETREP



***TOOLBARS


***HELPSTRINGS
M_NEWWH       [Opens new warehouse]
M_AISLEPROJ   [Displays the Aisle Information]
M_TBSLOT      [Displays Slotting menu]
M_REPPROJ     [Replaces a block with another block]
M_BAYPROJ     [Displays bay information]

//
//      End of AutoCAD menu file - C:\Program Files\AutoCAD R14\support\Slotting2.mns
//


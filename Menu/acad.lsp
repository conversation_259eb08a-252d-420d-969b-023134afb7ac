(defun INLIST ( lst str / ct tmpstr ret )
  (setq ct 0 str (strcase str))
  (repeat (length lst)
    (setq tmpstr (strcase (nth ct lst)))
    (if (wcmatch tmpstr (strcat "*" str "*"))
      (setq ret T)
    )
    (setq ct (1+ ct))
  )
  ret

)

;; Optimize Customizations
;; Note: always load the menu in the startup function
;; or you will get command interruption or incorrect 
;; command list parameter errors
(setvar "USERS5" "SUCCEED")
(setvar "cmdecho" 1)
;; pickadd control whether subsequent selects replace or add to the selection set
;; we need it to be on (add)
(setvar "PICKADD" 1)
;; Optimize Customizations
;; Check for Autocad 2000i or R14
(if (wcmatch (getvar "acadver") "16*") 

	(progn
		;; ACADLSPASDOC controls whether Autocad loads the acad.lsp file 
		;; between every drawing; for now we need it to
		(setvar "ACADLSPASDOC" 1)

		;; define the startup function which will
		;; run whenever a new drawing is loaded
		(defun-q OPTIMIZESTARTUP ()
			
			(command "filedia" 0)		;; turn off dialog boxes
			(command "UNDEFINE" "QUIT")	;; we will define our own quit function

			;; define a quit function that resets things to the way
			;; Autocad initially sets them so the user can run normal
			;; Autocad
			(defun-q C:QUIT ()
				(command "_menuunload" "slotting2")	;; unload our custom menu
				(command "filedia" 1)				;; turn on dialog boxes
				;; if the drawing is modified, quit without saving
				(if (eq (getvar "dbmod") 0)	
					(command ".quit")
					(command ".quit" "y")
				)
			)

			;; load our custom menu if necessary
			(if (not (MENUGROUP "SLOTTING2"))
				(command "_menuload" "SLOTTING2")
			)
		)

	)	;; end progn
	;; else R14 was detected
	(progn

		(princ "Detected Autocad R14\n")

		;; define the startup function which will
		;; run whenever a new drawing is loaded
		(defun OPTIMIZESTARTUP ()

			(command "filedia" 0)			;; turn off dialog boxes
			(command "UNDEFINE" "QUIT")		;; we will define our own quit function
			
			;; define a quit function that resets things to the way
			;; Autocad initially sets them so the user can run normal
			;; Autocad
			(defun C:QUIT ()
				(command "_menuunload" "slotting2")		;; unload our custom menus
				(command "filedia" 1)					;; turn on dialog boxes
				;; if the drawing is modified, quit without saving
				(if (eq (getvar "dbmod") 0)
					(command ".quit")
					(command ".quit" "y")
				)
			)

			;; load our custom menu if necessary
			(if (not (MENUGROUP "SLOTTING2"))
				(command "_menuload" "SLOTTING2")
			)

		)

	)	;; end progn
)	;; end if

;; append our startup function to the default one
(setq S::STARTUP (append S::STARTUP OPTIMIZESTARTUP))

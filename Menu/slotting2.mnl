;; Begin Optimize Customizations
;; turn off echoing

(setvar "cmdecho" 0)

;; The Optimize acad.lsp file should only get loaded when they use our icon because of the
;; /s (path) setting.  It sets USERS5 to "SUCCEED".  In this section, if USERS5 is set,
;; load the Optimize menus and application, otherwise unload the menu for normal Autocad
(if (eq "SUCCEED" (getvar "USERS5"))
	(progn
		(princ "Loading Optimize\n")
		(if (not (INLIST (arx) "modal") ) 
			(progn
				;; Remove all of our menus just in case
				;;				(if (wcmatch (getvar "acadver") "15*") 
				(if (wcmatch (getvar "acadver") "16*") 
					(setvar "SHORTCUTMENU" 0)
				)
				(menucmd "GSLOTTING2.WAREHOUSE=-");
				(menucmd "GSLOTTING2.PASSES=-");
				(menucmd "GSLOTTING2.REPORTS=-");
				(menucmd "GSLOTTING2.WIZARD=-");

				;;(menucmd "GSLOTTING2.POP0=+")
				;;(menucmd "GSLOTTING2.POP1=+")
				;;(menucmd "GSLOTTING2.POP3=+")
				;;(menucmd "GSLOTTING2.POP4=+")

				(menucmd "P1=+SLOTTING2.REPORTS")
				(menucmd "P1=+SLOTTING2.PASSES")
				(menucmd "P1=+SLOTTING2.WAREHOUSE")
				(menucmd "A1=ACAD.AUX1") 

				(princ "Loading Optimize modal.arx\n")
				(setvar "USERS3" "FIRST")
				(arxload "modal" nil)
				(setvar "USERS4" "LOADED")
			)
		)

		(if (eq "LOADED" (getvar "USERS4"))
			(progn
				(setvar "USERI1" 0)
				(command "UNDEFINE" "COPYCLIP")
				(command "UNDEFINE" "CUTCLIP")
				(command "UNDEFINE" "PASTECLIP")
				(command "UNDEFINE" "PASTESPEC")
				(command "UNDEFINE" "_U")
				(command "UNDEFINE" "_REDO")
				(command "UNDEFINE" "ERASE")
				(command "UNDEFINE" "COPYLINK")
				(command "UNDEFINE" "EXPLODE")
				(command "UNDEFINE" "FILLET")
				(command "UNDEFINE" "CHAMFER")
				(command "UNDEFINE" "BREAK")
				(command "UNDEFINE" "EXTEND")
				(command "UNDEFINE" "TRIM")
				(command "UNDEFINE" "LENGTHEN")
				(command "UNDEFINE" "STRETCH")
				(command "UNDEFINE" "SCALE")
				(command "UNDEFINE" "ROTATE")
				(command "UNDEFINE" "MOVE")
				(command "UNDEFINE" "ARRAY")
				(command "UNDEFINE" "OFFSET")
				(command "UNDEFINE" "MIRROR")
				;;(command "UNDEFINE" "INSERT")
				;;(command "UNDEFINE" "DDINSERT")
				;;(command "UNDEFINE" "DDRENAME")
				(command "UNDEFINE" "LAYER")
				(command "UNDEFINE" "OPEN")
				(command "UNDEFINE" "SAVE")
				(command "UNDEFINE" "SAVEAS")
				(command "UNDEFINE" "PURGE")
				;;(command "UNDEFINE" "UNITS")
				;(command "UNDEFINE" "DDUNITS")
				(command "UNDEFINE" "RENAME")
			)	;; end if loaded progn
		)	;; end if loaded

	)	;; end if Optimize progn
;; else, if running normal autocad, unload our menu
	(progn
		(if (menugroup "slotting2")
			(command "_menuunload" "slotting2")
		)
	)
)	;; end if Optimize

(if (eq "FIRST" (getvar "USERS3"))
	(progn
		(setvar "USERS3" "")
		(command "PROCESSLOGIN")
	)
) 
// End Optimize Customizations

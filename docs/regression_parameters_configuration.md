# 回归参数配置详解

## 概述

在SLOT仓储优化系统中，回归参数是Pass1货架选择算法的核心组件。这些参数通过回归分析确定产品与货架类型之间的最佳匹配关系，实现仓储布局的优化。

## 回归参数类型

### 核心参数
- **Line.a**：回归线斜率，反映扩展立方体与库存量的相关性强度
- **Line.b**：回归线截距，调整整体回归线的位置
- **Y.hi**：上限偏差，定义回归线上方的"热区"边界
- **Y.low**：下限偏差，定义回归线下方的"热区"边界

### 边界参数
- **X.hi/X.low**：扩展立方体的上下限
- **B.hi/B.low**：库存量的上下限

## 参数配置方式

### 1. 硬编码默认参数
基于历史仓储数据分析得出的经验值：

```cpp
static int OptFlag = OPTIMIZE_FOR_CUBE;     /* default: CUBE */
static double regression_a = 0.9720788;     /* "A" regression coef for logs */
static double regression_b = 0.61786669;    /* "B" regression coef for logs */
static double upper_y = 0.22;               /* distance to upper bound from rgr line */
static double lower_y = -0.02;              /* distance to lower bound from rgr line */
```

### 2. 动态计算参数
系统根据当前设施的货架类型数据实时计算：

```cpp
// 最小二乘法回归计算
val = DataCount * sumsq_x - sum_x * sum_x;
a_coeff = (DataCount * sum_xy - sum_x * sum_y) / val;
b_coeff = (sumsq_x * sum_y - sum_x * sum_xy) / val;

// 计算偏差范围
for (n=0; n<DataCount; n++) {
    predicted_y = logxCube[n] * a_coeff + b_coeff;
    deviation = logxBOH[n] - predicted_y;
    if (deviation > highval_y) highval_y = deviation;
    if (deviation < lowval_y) lowval_y = deviation;
}

// 存储参数
rackGp[i].Line.a = a_coeff;
rackGp[i].Line.b = b_coeff;
rackGp[i].Y.hi = highval_y;
rackGp[i].Y.low = fabs(lowval_y);
```

### 3. 配置优先级
1. **注册表设置** > **用户配置文件** > **动态计算** > **硬编码默认值**
2. **度量单位影响**：英制使用对数转换，公制使用线性计算

## 具体计算示例

### 输入数据（货架类型表）
| 货架类型 | XCube | BOH | 描述 |
|----------|-------|-----|------|
| 类型1 | 1.41 | 5.63 | Case Static Shelf, 1 Facing |
| 类型2 | 4.22 | 16.88 | Case Static Shelf, 2 Facings |
| 类型3 | 5.29 | 21.16 | Case Flow Rack, 1 Facing |
| 类型4 | 7.03 | 28.13 | Case Static Shelf, 3 Facings |
| 类型5 | 9.84 | 39.38 | Case Static Shelf, 4 Facings |

### Step 1: 对数转换（英制系统）
```cpp
logxCube = log10(XCube);
logxBOH = log10(BOH);
```

**转换结果：**
| 货架类型 | log(XCube) | log(BOH) |
|----------|------------|----------|
| 类型1 | 0.149 | 0.750 |
| 类型2 | 0.625 | 1.227 |
| 类型3 | 0.723 | 1.325 |
| 类型4 | 0.847 | 1.449 |
| 类型5 | 0.993 | 1.595 |

### Step 2: 最小二乘法回归计算
```
统计量计算：
sum_x = Σlog(XCube) = 3.337
sum_y = Σlog(BOH) = 6.346
sum_xy = Σ(log(XCube) × log(BOH)) = 4.512
sumsq_x = Σ(log(XCube))² = 2.567
DataCount = 5

回归系数计算：
val = 5 × 2.567 - 3.337² = 1.700
a_coeff = (5 × 4.512 - 3.337 × 6.346) / 1.700 = 0.813
b_coeff = (2.567 × 6.346 - 3.337 × 4.512) / 1.700 = 0.728
```

### Step 3: 偏差范围计算
| 货架类型 | 预测值 | 实际值 | 偏差 |
|----------|--------|--------|------|
| 类型1 | 0.849 | 0.750 | -0.099 |
| 类型2 | 1.236 | 1.227 | -0.009 |
| 类型3 | 1.316 | 1.325 | +0.009 |
| 类型4 | 1.417 | 1.449 | +0.032 |
| 类型5 | 1.535 | 1.595 | +0.060 |

**最终参数：**
```
Line.a = 0.813 (斜率)
Line.b = 0.728 (截距)
Y.hi = 0.060 (最大正偏差)
Y.low = 0.099 (最大负偏差的绝对值)
```

## 参数应用逻辑

### 热区概念
回归线周围的区域被定义为"热区"，在此区域内的产品保持原始的扩展立方体和库存量值。

```cpp
// 计算回归线预测值
Rgr_Y = tptr->Line.a * XCube + tptr->Line.b;

// 检查是否在热区内
if (Rgr_Y + tptr->Y.hi < BOH) {
    // 上限区域 - 根据OptFlag调整
    if (OptFlag == 0) {
        // 优化立方体利用率 - 调整XCube
        XCube = (BOH - tptr->Line.b - tptr->Y.hi) / tptr->Line.a;
    } else {
        // 优化劳动效率 - 调整BOH
        BOH = Rgr_Y + tptr->Y.hi;
    }
} else if (Rgr_Y - tptr->Y.low > BOH) {
    // 下限区域 - 根据OptFlag调整
    if (OptFlag == 0) {
        XCube = (BOH - tptr->Line.b - tptr->Y.low) / tptr->Line.a;
    } else {
        BOH = Rgr_Y - tptr->Y.low;
    }
}
```

### 优化标志（OptFlag）
- **OptFlag = 0**：优化立方体利用率，选择更紧凑的货架
- **OptFlag = 1**：优化劳动效率，选择更便捷的货架

## 实际应用示例

### 产品优化过程
**产品A（高移动量）：**
```
初始值：XCube = 890, BOH = 150
回归预测：Rgr_Y = 0.813 × log10(890) + 0.728 = 3.133
热区检查：
- 上限：3.133 + 0.060 = 3.193
- 下限：3.133 - 0.099 = 3.034
- log10(150) = 2.176 < 下限

OptFlag = 1（优化劳动效率）：
调整BOH = 10^3.034 = 1082
结果：选择更大容量的流动货架
```

**产品B（低移动量）：**
```
初始值：XCube = 4.4, BOH = 200
回归预测：Rgr_Y = 0.813 × log10(4.4) + 0.728 = 1.249
热区检查：
- 上限：1.249 + 0.060 = 1.309
- 下限：1.249 - 0.099 = 1.150
- log10(200) = 2.301 > 上限

OptFlag = 0（优化立方体利用率）：
调整XCube = (2.301 - 0.728 - 0.060) / 0.813 = 1.859
结果：选择更紧凑的储存货架
```

## 参数意义和影响

### Line.a（斜率）
- **值越大**：扩展立方体对货架选择的影响越大
- **值越小**：库存量对货架选择的影响相对更大
- **典型范围**：0.5 - 1.5

### Y.hi/Y.low（偏差范围）
- **范围越大**：算法越宽松，更多产品保持原始值
- **范围越小**：算法越严格，更多产品被调整优化
- **典型范围**：0.02 - 0.5

## 配置建议

### 新设施配置
1. **使用默认参数**开始运行
2. **收集3-6个月**的实际运营数据
3. **重新计算回归参数**以适应具体环境
4. **定期更新参数**以保持优化效果

### 参数调优
- **提高效率**：增大Y.hi/Y.low范围，减少调整频率
- **提高精度**：减小Y.hi/Y.low范围，增加优化强度
- **平衡考虑**：根据实际成本效果调整OptFlag设置

## 总结

回归参数配置是仓储优化算法的核心，通过科学的统计分析和灵活的配置机制，确保系统能够适应不同的仓储环境和优化目标。正确的参数配置可以显著提高仓储效率和降低操作成本。

# Pass1 优化算法：货架类型分配（Rack Assignment）详细分析

## 概述

Pass1 是 Succeed 系统优化流程的第一步，负责为每个产品分配理想的货架类型（Ideal Rack Type）和当前设施中可用的最佳货架类型（Best Available Rack Type）。这个过程基于产品的特性（如尺寸、重量、销售量等）和可用的货架配置。

## 核心组件

Pass1 优化算法主要由以下组件实现：

1. `SLOTPass1Manager` - 服务器端管理类
2. `Pass1Process` - 引擎端处理类
3. 各种数据结构和辅助函数

## 详细步骤

### 1. 初始化和数据准备

#### 1.1 数据收集
- 从数据库收集产品信息
- 收集货架类型信息
- 收集设施中可用的货架位置信息

#### 1.2 建立连接
```cpp
// 在 SLOTPass1Manager::RunPass 中
ExternalConnection aConnection = ExternalConnectionFactory.CreateConnection(pClientName);
```

#### 1.3 发送参数
```cpp
// 在 SLOTPass1Manager::SendPass1Parameters 中
SendPass1Parameters(aConnection, pClientName, pOptions);
```

### 2. 发送货架类型数据

#### 2.1 发送货架类型使用规则
```cpp
// 在 SLOTPass1Manager::SendPass1ProfileRules 中
SendPass1ProfileRules(aConnection, theRackTypeUsageList);
```

这些规则定义了每种货架类型的使用条件，包括：
- 货架配置ID（Bay Profile ID）
- 货架类型（Bay Type）
- 面向信息ID（Bay Facing Info ID）
- 超级组（Super Group）- 区分危险品和非危险品
- 面数（Facing Count）
- 扩展立方体（Extended Cube）
- 扩展库存（Extended BOH）

#### 2.2 发送货架配置数据
```cpp
// 在 SLOTPass1Manager::SendPass1Profiles 中
SendPass1Profiles(theP1RackDataList, aConnection);
```

这些数据描述了当前设施中可用的货架配置：
- 货架配置ID
- 货架类型
- 描述
- 可用线性单位（Available Lineal Units）
- 可用固定面（Available Fixed Facings）

#### 2.3 发送产品组驱动参数
```cpp
// 在 SLOTPass1Manager::SendPass1ProductGroupDriveParams 中
SendPass1ProductGroupDriveParams(aConnection, theDriveList);
```

### 3. 发送产品数据

```cpp
// 在 SLOTPass1Manager::SendPass1Products 中
SendPass1Products(theProductPackList, aConnection, aIdealRackArray, aAvailRackArray);
```

对于每个产品，发送：
- 产品ID
- 描述
- 宽度、深度、高度
- 重量
- 是否危险品
- 扩展立方体（Extended Cube）
- 扩展库存（Extended BOH）
- 优化方式（Optimize By）- 立方体或劳动力

### 4. 引擎处理

在 `Pass1Process::Execute()` 中执行主要的处理逻辑：

#### 4.1 加载货架类型
```cpp
loadRackTypes();
```

将接收到的货架类型数据加载到内存中的数据结构。

#### 4.2 加载产品
```cpp
loadProducts();
```

将接收到的产品数据加载到内存中的数据结构。

#### 4.3 为每个产品分配货架类型

对于每个产品：

```cpp
for (i=0; i<numProducts; i++) {
    // 获取理想和可用的货架类型
    getCost(&prodPk[i], &prodRackIdeal[i], &prodRackAvail[i*numRanks], numRanks, passOutMsg);
}
```

在 `getCost()` 函数中：

1. 调整产品值
   ```cpp
   adjustProductValues(aProd, tptr);
   ```
   
2. 查找理想货架类型
   ```cpp
   findIdealRack(aProd, rackIdeal, tptr);
   ```
   
3. 查找最佳可用货架类型
   ```cpp
   findAvailRack(aProd, rackAvail, numRanks, tptr, passMsg);
   ```

#### 4.4 排序和优化

对于每个产品的可用货架类型排名：

```cpp
if (SortRankingFlag) {
    sortRankings(rackAvail, numAvailRanks);
}
```

排序基于：
- 处理方法（降序）
- 高度差异（升序）

### 5. 结果处理

#### 5.1 发送理想货架类型结果
```cpp
sprintf(line,"Y|I|%d|%s|%d|%d|%d|%s|%d|%s|%s|\n",
    prodPk[i].dbID,
    prodPk[i].desc,
    prodRackIdeal[i].facingCount,
    prodRackIdeal[i].idealProfileID,
    prodRackIdeal[i].idealLevelType,
    basicTy[idealIdx].desc,
    (int)(prodRackIdeal[i].facingCount * prodPk[i].width),
    prodPk[i].WMSProdID,
    prodPk[i].WMSProdDetID);
```

#### 5.2 发送最佳可用货架类型结果
```cpp
sprintf(line,"Y|B|%d|%s|%d|%d|%d|%d|%d|%d|%s|%d|%s|%s|%d|%d|%d|\n",
    prodPk[i].dbID,
    prodPk[i].desc,
    prodRackAvail[i*numRanks+p].facingCount,
    prodRackAvail[i*numRanks+p].availProfileID,
    prodRackAvail[i*numRanks+p].availLevelType,
    prodRackAvail[i*numRanks+p].linealFacingCount,
    prodRackAvail[i*numRanks+p].linealFacingWidth,
    p+1,
    basicTy[idealIdx].desc,
    0, prodPk[i].WMSProdID, prodPk[i].WMSProdDetID,
    prodRackAvail[i*numRanks+p].origHandling,
    prodRackAvail[i*numRanks+p].actualHandling,
    prodRackAvail[i*numRanks+p].fits);
```

#### 5.3 发送货架使用摘要
```cpp
sprintf(line,"Y|L|%d|%d|%s|%d|%d|%d|%d|%d|%d|\n",
    basicTy[i].profileID,
    basicTy[i].levelType,
    basicTy[i].desc,
    (int)basicTy[i].startLinealFacings,
    (int)basicTy[i].avlLinealFacings,
    basicTy[i].startFixedFaces,
    basicTy[i].avlFixedFaces,
    basicTy[i].countNeeded,
    basicTy[i].totFacings);
```

### 6. 服务器端处理结果

在 `SLOTPass1Manager::ProcessEngineResults` 中：

1. 解析引擎返回的结果
2. 将结果存储到数据库
3. 生成摘要报告

## 关键算法

### 产品适配检查

```cpp
int Pass1Process::willProductFit(prodPack *aProd, TABLE_ENTRY profile, int tryCaseHandling)
```

检查产品是否适合特定的货架配置，考虑：
- 产品尺寸
- 货架尺寸
- 处理方法（箱式或托盘式）
- 危险品限制

### 货架类型排序

```cpp
void Pass1Process::sortRankings(struct availResult *rackAvail, int numAvailRanks)
```

根据处理方法和高度差异对可用货架类型进行排序。

### 空间使用计算

```cpp
int Pass1Process::useSpace(P1DATA *tptr, int idx, double prodWidth, int UOI)
```

计算并更新使用特定货架类型后的可用空间。

## 优化参数

Pass1 优化可通过以下参数调整：

1. **多重因子（Multiple Factor）** - 用于调整扩展立方体和扩展库存值
2. **排序标志（Sort Ranking Flag）** - 是否对可用货架类型进行排序
3. **最大排名数（Max Rankings）** - 为每个产品返回的最大可用货架类型数
4. **优化方式（Optimize By）** - 基于立方体或劳动力优化

## 数据结构

### 产品包（Product Pack）
```cpp
struct prodPack {
    int dbID;
    char desc[256];
    double width;
    double length;
    double height;
    double weight;
    int hazard;
    double xCube;
    double xBOH;
    double cube;
    double movement;
    int optBy;
    char WMSProdID[256];
    char WMSProdDetID[256];
    int trace;
    double containerWidth;
};
```

### 基本货架类型（Basic Rack Type）
```cpp
struct basicRackType {
    int profileID;
    int levelType;
    char desc[256];
    float startLinealFacings;
    float avlLinealFacings;
    int startFixedFaces;
    int avlFixedFaces;
    int countNeeded;
    int totFacings;
    int inFacility;
    int isHazard;
    float maxWidth;
    float maxDepth;
    float maxHeight;
    float maxWeight;
    int rejected;
    int handlingMethod;
    float productGap;
    float productSnap;
    float facingGap;
    float facingSnap;
};
```

### 理想结果（Ideal Result）
```cpp
struct idealResult {
    int facingCount;
    int idealProfileID;
    int idealLevelType;
    double xBOH;
    double xCube;
};
```

### 可用结果（Available Result）
```cpp
struct availResult {
    int productPackID;
    int facingCount;
    int availProfileID;
    int availLevelType;
    int ranking;
    double xBOH;
    double xCube;
    double diffxCube;
    double diffxBOH;
    int linealFacingCount;
    int linealFacingWidth;
    int profileIdx;
    double heightDiff;
    int origHandling;
    int actualHandling;
    int fits;
    int isDuplicate;
};
```

## 总结

Pass1 优化算法是一个复杂的过程，它考虑了产品特性、货架配置和设施限制，为每个产品分配最合适的货架类型。该算法通过比较产品的扩展立方体和扩展库存与预定义的货架类型使用规则，找到理想的匹配。然后，它考虑当前设施中的可用空间，为产品分配最佳可用的货架类型。

这个过程的结果是每个产品都有一个理想的货架类型（可能在当前设施中不可用）和一个或多个排名的最佳可用货架类型。这些信息将用于后续的优化步骤，如产品布局和移动计划。
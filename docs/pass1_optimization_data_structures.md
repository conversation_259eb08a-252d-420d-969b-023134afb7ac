# Pass1 优化算法数据结构详解

## 核心数据结构

Pass1 优化算法使用多种数据结构来表示产品、货架类型和优化结果。以下是这些数据结构的详细说明。

### 1. 产品包（Product Pack）

```cpp
struct prodPack {
    int dbID;                // 数据库ID
    char desc[256];          // 产品描述
    double width;            // 宽度
    double length;           // 长度
    double height;           // 高度
    double weight;           // 重量
    int hazard;              // 是否危险品 (1=是, 0=否)
    double xCube;            // 扩展立方体 (Extended Cube)
    double xBOH;             // 扩展库存 (Extended Balance on Hand)
    double cube;             // 立方体 (Cube)
    double movement;         // 移动量 (Movement)
    int optBy;               // 优化方式 (0=立方体, 1=劳动力)
    char WMSProdID[256];     // WMS产品ID
    char WMSProdDetID[256];  // WMS产品详情ID
    int trace;               // 跟踪标志
    double containerWidth;   // 容器宽度
};
```

这个结构存储了产品的所有相关信息，包括物理特性（尺寸、重量）、库存信息（xCube、xBOH）和分类信息（是否危险品）。

### 2. 表条目（Table Entry）

```cpp
struct table_entry {
    int profileID;           // 货架配置ID
    int levelType;           // 层级类型
    int facingID;            // 面向信息ID
    int superGroup;          // 超级组 (0=危险品, 4=常规)
    int facingCount;         // 面数
    double xCube;            // 扩展立方体
    double xBOH;             // 扩展库存
    double logxCube;         // xCube的对数值
    double logxBOH;          // xBOH的对数值
    double diffxCube;        // 货架xCube与产品xCube的差异
    double diffxBOH;         // 货架xBOH与产品xBOH的差异
    double actualDiff;       // 货架与产品点之间的线性距离
    char desc[256];          // 描述
};
```

这个结构表示货架类型使用规则，定义了不同货架类型的特性和使用条件。

### 3. 基本货架类型（Basic Rack Type）

```cpp
struct basicRackType {
    int profileID;               // 货架配置ID
    int levelType;               // 层级类型
    char desc[256];              // 描述
    float startLinealFacings;    // 初始线性面数
    float avlLinealFacings;      // 可用线性面数
    int startFixedFaces;         // 初始固定面数
    int avlFixedFaces;           // 可用固定面数
    int countNeeded;             // 需要的数量
    int totFacings;              // 总面数
   
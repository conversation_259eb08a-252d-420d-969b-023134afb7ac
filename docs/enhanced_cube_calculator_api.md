# 增强立方体计算器API文档

## 概述

`SLOTEnhancedCubeCalculator` 是对原有立方体计算功能的扩展和增强，提供了多种立方体计算方法、优化算法和分析工具，以满足现代仓储管理的复杂需求。

## 类结构

### 核心类
```cpp
class SLOTEnhancedCubeCalculator : public SLOTDataView
```

### 枚举类型

#### 立方体计算类型
```cpp
enum CubeCalculationType {
    CUBE_TYPE_BASIC = 0,        // 基础立方体计算
    CUBE_TYPE_EXTENDED = 1,     // 扩展立方体计算（考虑移动量）
    CUBE_TYPE_WEIGHTED = 2,     // 加权立方体计算
    CUBE_TYPE_DYNAMIC = 3,      // 动态立方体计算
    CUBE_TYPE_PREDICTIVE = 4,   // 预测立方体计算
    CUBE_TYPE_OPTIMIZED = 5     // 优化立方体计算
};
```

### 数据结构

#### 计算参数结构
```cpp
struct CubeCalculationParams {
    double conversionFactor;    // 转换因子（默认：1728.0）
    double weightFactor;        // 重量因子（默认：1.0）
    double densityFactor;       // 密度因子（默认：1.0）
    double seasonalFactor;      // 季节性因子（默认：1.0）
    double growthFactor;        // 增长因子（默认：1.0）
    bool includeMovement;       // 是否包含移动量（默认：true）
    bool includeWeight;         // 是否包含重量（默认：false）
    bool optimizeSpace;         // 是否优化空间（默认：false）
};
```

#### 计算结果结构
```cpp
struct CubeCalculationResult {
    double basicCube;           // 基础立方体
    double extendedCube;        // 扩展立方体
    double weightedCube;        // 加权立方体
    double optimizedCube;       // 优化立方体
    double utilizationRate;     // 利用率
    double efficiency;          // 效率
    CString calculationMethod;  // 计算方法
    CString notes;              // 备注
};
```

#### 统计信息结构
```cpp
struct CubeStatistics {
    double totalCube;           // 总立方体
    double averageCube;         // 平均立方体
    double maxCube;             // 最大立方体
    double minCube;             // 最小立方体
    double standardDeviation;   // 标准差
    double variance;            // 方差
    int itemCount;              // 项目数量
};
```

## 主要方法

### 基础立方体计算

#### CalculateBasicCube
```cpp
// 基于尺寸计算基础立方体
double CalculateBasicCube(double width, double height, double depth, 
                         double conversionFactor = 1728.0);

// 基于产品包装计算基础立方体
double CalculateBasicCube(const SLOTProductPack& productPack, 
                         const CubeCalculationParams& params = CubeCalculationParams());
```

**使用示例：**
```cpp
SLOTEnhancedCubeCalculator calculator;

// 方法1：直接尺寸计算
double cube1 = calculator.CalculateBasicCube(24.0, 16.0, 8.0, 1728.0);
// 结果：1.78 ft³

// 方法2：产品包装计算
CubeCalculationParams params;
params.conversionFactor = 1728.0;
double cube2 = calculator.CalculateBasicCube(productPack, params);
```

### 扩展立方体计算

#### CalculateExtendedCube
```cpp
double CalculateExtendedCube(const SLOTProductPack& productPack, 
                            double movement, 
                            const CubeCalculationParams& params = CubeCalculationParams());
```

**使用示例：**
```cpp
// 计算扩展立方体
double extendedCube = calculator.CalculateExtendedCube(productPack, 500.0, params);
// 对于移动量500的产品，结果：890 ft³
```

### 高级计算方法

#### CalculateWeightedCube
```cpp
double CalculateWeightedCube(const SLOTProductPack& productPack, 
                            double weightFactor, double densityFactor,
                            const CubeCalculationParams& params = CubeCalculationParams());
```

#### CalculateDynamicCube
```cpp
double CalculateDynamicCube(const SLOTProductPack& productPack, 
                           double timeHorizon, double seasonalFactor,
                           const CubeCalculationParams& params = CubeCalculationParams());
```

#### CalculatePredictiveCube
```cpp
double CalculatePredictiveCube(const SLOTProductPack& productPack, 
                              double growthRate, double forecastPeriod,
                              const CubeCalculationParams& params = CubeCalculationParams());
```

### 综合计算方法

#### CalculateComprehensiveCube
```cpp
CubeCalculationResult CalculateComprehensiveCube(const SLOTProductPack& productPack,
                                                CubeCalculationType type = CUBE_TYPE_EXTENDED,
                                                const CubeCalculationParams& params = CubeCalculationParams());
```

**使用示例：**
```cpp
// 综合计算
CubeCalculationResult result = calculator.CalculateComprehensiveCube(
    productPack, CUBE_TYPE_EXTENDED, params);

cout << "基础立方体: " << result.basicCube << " ft³" << endl;
cout << "扩展立方体: " << result.extendedCube << " ft³" << endl;
cout << "利用率: " << result.utilizationRate << "%" << endl;
cout << "计算方法: " << result.calculationMethod << endl;
```

### 批量计算

#### CalculateBatchCubes
```cpp
std::vector<CubeCalculationResult> CalculateBatchCubes(
    const std::vector<SLOTProductPack>& productPacks,
    CubeCalculationType type = CUBE_TYPE_EXTENDED,
    const CubeCalculationParams& params = CubeCalculationParams());
```

**使用示例：**
```cpp
// 批量计算多个产品
std::vector<SLOTProductPack> products = {product1, product2, product3};
std::vector<CubeCalculationResult> results = calculator.CalculateBatchCubes(
    products, CUBE_TYPE_EXTENDED, params);

for (const auto& result : results) {
    cout << "扩展立方体: " << result.extendedCube << " ft³" << endl;
}
```

### 优化算法

#### OptimizeCubeUtilization
```cpp
double OptimizeCubeUtilization(const std::vector<SLOTProductPack>& productPacks,
                              const std::vector<SLOTLocation>& locations);
```

#### OptimizeSpaceAllocation
```cpp
std::map<int, int> OptimizeSpaceAllocation(const std::vector<SLOTProductPack>& productPacks,
                                          const std::vector<SLOTLocation>& locations);
```

**使用示例：**
```cpp
// 优化空间分配
std::vector<SLOTProductPack> products;
std::vector<SLOTLocation> locations;

std::map<int, int> allocation = calculator.OptimizeSpaceAllocation(products, locations);
// 返回产品ID到位置ID的映射
```

### 分析工具

#### CalculateCubeStatistics
```cpp
CubeStatistics CalculateCubeStatistics(const std::vector<double>& cubes);
```

#### GenerateCubeReport
```cpp
CString GenerateCubeReport(const std::vector<CubeCalculationResult>& results);
```

**使用示例：**
```cpp
// 统计分析
std::vector<double> cubes = {890.0, 112.5, 4.4};
CubeStatistics stats = calculator.CalculateCubeStatistics(cubes);

cout << "总立方体: " << stats.totalCube << " ft³" << endl;
cout << "平均立方体: " << stats.averageCube << " ft³" << endl;
cout << "标准差: " << stats.standardDeviation << endl;

// 生成报告
CString report = calculator.GenerateCubeReport(results);
```

### 工具方法

#### ConvertCubeUnits
```cpp
double ConvertCubeUnits(double cube, const CString& fromUnit, const CString& toUnit);
```

#### CalculateDensity
```cpp
double CalculateDensity(double weight, double cube);
```

#### CalculateVolumeEfficiency
```cpp
double CalculateVolumeEfficiency(double usedCube, double totalCube);
```

**使用示例：**
```cpp
// 单位转换
double cubeFeet = calculator.ConvertCubeUnits(1000.0, "cubic_inches", "cubic_feet");
// 结果：0.578 ft³

// 密度计算
double density = calculator.CalculateDensity(25.0, 1.78);
// 结果：14.04 lbs/ft³

// 体积效率
double efficiency = calculator.CalculateVolumeEfficiency(267.0, 300.0);
// 结果：89%
```

## 配置和设置

### 设置默认参数
```cpp
void SetDefaultParams(const CubeCalculationParams& params);
CubeCalculationParams GetDefaultParams() const;
```

### 设置转换因子
```cpp
void SetConversionFactor(double factor);
double GetConversionFactor() const;
```

**使用示例：**
```cpp
// 设置默认参数
CubeCalculationParams defaultParams;
defaultParams.conversionFactor = 1728.0;
defaultParams.includeMovement = true;
defaultParams.optimizeSpace = true;
calculator.SetDefaultParams(defaultParams);

// 设置转换因子
calculator.SetConversionFactor(1728.0); // 立方英寸到立方英尺
```

## 完整使用示例

```cpp
#include "SLOTEnhancedCubeCalculator.h"

void ExampleUsage() {
    // 创建计算器实例
    SLOTEnhancedCubeCalculator calculator;
    
    // 设置计算参数
    CubeCalculationParams params;
    params.conversionFactor = 1728.0;
    params.includeMovement = true;
    params.weightFactor = 1.2;
    params.optimizeSpace = true;
    
    // 创建产品包装对象（假设已初始化）
    SLOTProductPack productPack;
    
    // 1. 基础立方体计算
    double basicCube = calculator.CalculateBasicCube(productPack, params);
    
    // 2. 扩展立方体计算
    double extendedCube = calculator.CalculateExtendedCube(productPack, 500.0, params);
    
    // 3. 综合计算
    CubeCalculationResult result = calculator.CalculateComprehensiveCube(
        productPack, CUBE_TYPE_EXTENDED, params);
    
    // 4. 批量计算
    std::vector<SLOTProductPack> products = {productPack};
    std::vector<CubeCalculationResult> batchResults = 
        calculator.CalculateBatchCubes(products, CUBE_TYPE_EXTENDED, params);
    
    // 5. 统计分析
    std::vector<double> cubes;
    for (const auto& res : batchResults) {
        cubes.push_back(res.extendedCube);
    }
    CubeStatistics stats = calculator.CalculateCubeStatistics(cubes);
    
    // 6. 生成报告
    CString report = calculator.GenerateCubeReport(batchResults);
    
    // 输出结果
    printf("基础立方体: %.2f ft³\n", basicCube);
    printf("扩展立方体: %.2f ft³\n", extendedCube);
    printf("利用率: %.2f%%\n", result.utilizationRate);
    printf("平均立方体: %.2f ft³\n", stats.averageCube);
}
```

## 错误处理

### 常见错误
1. **无效尺寸**：宽度、高度或深度 ≤ 0
2. **无效转换因子**：转换因子 ≤ 0
3. **空产品列表**：批量计算时产品列表为空
4. **内存不足**：大批量计算时内存不足

### 错误检查
```cpp
// 参数验证
bool ValidateInputParameters(double width, double height, double depth);
bool ValidateCubeCalculation(const CubeCalculationResult& result);
```

## 性能考虑

### 优化建议
1. **批量计算**：对多个产品使用批量方法而非循环调用
2. **参数复用**：重复使用相同的计算参数对象
3. **内存管理**：及时清理大型结果集合
4. **缓存结果**：对相同输入缓存计算结果

### 性能指标
- **单次计算**：< 1ms
- **批量计算**：约 0.1ms/产品
- **内存使用**：约 100KB/1000个产品结果

## 总结

`SLOTEnhancedCubeCalculator` 提供了全面的立方体计算功能，支持多种计算类型、优化算法和分析工具。通过灵活的参数配置和丰富的API接口，能够满足现代仓储管理系统的各种需求。

# 战略模式（Strategic Mode）详细计算步骤

战略模式是Succeed系统中最全面的优化方法，它从零开始创建最优布局，不考虑现有产品位置。本文档详细说明战略模式的计算步骤，并提供具体数值示例。

## 1. 算法概述

战略模式的核心是`RunComp()`函数，它执行以下主要步骤：
1. 产品排序
2. 位置排序
3. 产品分配
4. 成本计算

## 2. 详细计算步骤

### 2.1 初始化

```cpp
int Pass4Process::RunComp()
{
    double CostSum = 0;
    int numTimes = 1;
    
    // 初始化所有位置为未分配
    for (int i = 0; i < mem_loc_count; i++) {
        mem_locs[i].Assigned = 0;
    }
    
    // 初始化所有产品为未分配
    for (int i = 0; i < mem_prod_count; i++) {
        mem_prods[i].newLocIdx = -1;
    }
    
    // 初始化货架和层级权重
    for (int i = 0; i < mem_bay_count; i++) {
        mem_bay_weights[i].currentBayWeight = 0;
    }
    
    for (int i = 0; i < mem_level_count; i++) {
        mem_level_weights[i].currentLevelWeight = 0;
    }
}
```

### 2.2 产品排序

产品按照优先级指标排序，通常是销售量（movement）或立方体（cube）。

```cpp
// 按销售量降序排序产品
qsort(mem_prods, mem_prod_count, sizeof(p4ProdPack), CompareProductsByMovement);
```

排序函数示例：
```cpp
int CompareProductsByMovement(const void *a, const void *b)
{
    p4ProdPack *p1 = (p4ProdPack *)a;
    p4ProdPack *p2 = (p4ProdPack *)b;
    
    if (p1->movement > p2->movement) return -1;
    if (p1->movement < p2->movement) return 1;
    return 0;
}
```

### 2.3 位置排序

位置按照优先级指标排序，通常是到拣选点的距离。

```cpp
// 按到拣选点距离升序排序位置
qsort(mem_locs, mem_loc_count, sizeof(p4LocPack), CompareLocationsByDistance);
```

排序函数示例：
```cpp
int CompareLocationsByDistance(const void *a, const void *b)
{
    p4LocPack *l1 = (p4LocPack *)a;
    p4LocPack *l2 = (p4LocPack *)b;
    
    if (l1->sel_dist < l2->sel_dist) return -1;
    if (l1->sel_dist > l2->sel_dist) return 1;
    return 0;
}
```

### 2.4 产品分配

对于每个产品，找到最佳位置并分配。

```cpp
for (int p = 0; p < mem_prod_count; p++) {
    // 跳过已分配的产品
    if (mem_prods[p].newLocIdx >= 0)
        continue;
    
    // 对于非变宽产品
    if (mem_prods[p].VarWidth == 0) {
        double bestCost = 999999999.0;
        int bestLoc = -1;
        
        // 尝试每个未分配的位置
        for (int l = 0; l < mem_loc_count; l++) {
            if (mem_locs[l].Assigned == 1)
                continue;
            
            // 检查产品是否适合位置
            int casesFit = MaxCaseFit(&mem_prods[p], &mem_locs[l]);
            if (casesFit <= 0)
                continue;
            
            // 计算成本
            char costBuffer[COST_BUFFER_SIZE];
            double tmpCost = GetCost(p, l, casesFit, costBuffer);
            
            // 更新最佳位置
            if (tmpCost < bestCost) {
                bestCost = tmpCost;
                bestLoc = l;
            }
        }
        
        // 分配产品到最佳位置
        if (bestLoc >= 0) {
            mem_prods[p].newLocIdx = bestLoc;
            mem_locs[bestLoc].newProdIdx = p;
            mem_locs[bestLoc].Assigned = 1;
            
            // 更新货架和层级权重
            int caseCount = MaxCaseFit(&mem_prods[p], &mem_locs[bestLoc]);
            int bayIdx = FindBay(mem_locs[bestLoc].BayID);
            if (bayIdx >= 0) {
                mem_bay_weights[bayIdx].currentBayWeight += 
                    caseCount * mem_prods[p].caseWeight;
            }
            
            int levelIdx = FindLevel(mem_locs[bestLoc].Level_dbid);
            if (levelIdx >= 0) {
                mem_level_weights[levelIdx].currentLevelWeight += 
                    caseCount * mem_prods[p].caseWeight;
            }
            
            CostSum += bestCost;
        }
    }
    // 对于变宽产品的处理...
}
```

### 2.5 成本计算

成本计算是战略模式的核心，它考虑多种因素：
- 拣选距离
- 拣选时间
- 处理时间
- 叉车操作
- 存储工人操作

```cpp
double Pass4Process::GetCost(int pi, int li, int casesInPick, char *buffer)
{
    double c1;
    p4ProdPack p;
    p4LocPack l;
    
    // 复制产品和位置数据
    memcpy(&p, &mem_prods[pi], sizeof(p4ProdPack));
    memcpy(&l, &mem_locs[li], sizeof(p4LocPack));
    
    // 设置产品的单位发货
    p.unitOfIssue = mem_prods[pi].unitOfIssue;
    p.productTi = mem_prods[pi].productTi;
    p.productHi = mem_prods[pi].productHi;
    p.caseWeight = mem_prods[pi].caseWeight;
    
    // 计算劳动成本
    c1 = CalcLabor(&p, &l, casesInPick, buffer);
    
    return c1;
}
```

劳动成本计算：
```cpp
double Pass4Process::CalcLabor(p4ProdPack *p, p4LocPack *l, int casesInPick, char *buffer)
{
    double Cost = 0.0;
    double selectionTotalTravelTime = 0.0;
    double selectionPickHandlingTime = 0.0;
    double selectionStockerHandlingTime = 0.0;
    double fullPalletTotalTravelTime = 0.0;
    double fullPalletHandlingTime = 0.0;
    double casesPerHour = 0.0;
    
    // 计算拣选距离和时间
    selectionTotalTravelTime = p->caseMovement * l->sel_dist / selectionSpeed;
    
    // 计算拣选处理时间
    selectionPickHandlingTime = p->caseMovement * pickHandlingTime;
    
    // 计算存储工人处理时间
    if (l->levelType != BAY_TYPE_FLOW)
        selectionStockerHandlingTime = p->caseMovement * stockerHandlingTime;
    
    // 计算叉车距离和时间
    if (p->palletMovement > 0) {
        fullPalletTotalTravelTime = p->palletMovement * l->fork_dist / forkSpeed;
        fullPalletHandlingTime = p->palletMovement * forkHandlingTime;
    }
    
    // 计算每小时箱数
    double nonForkTime = selectionTotalTravelTime + selectionPickHandlingTime + selectionStockerHandlingTime;
    if (nonForkTime > 0)
        casesPerHour = p->caseMovement / nonForkTime;
    
    double forkTime = fullPalletTotalTravelTime + fullPalletHandlingTime;
    if (forkTime > 0)
        casesPerHour += p->palletMovement * p->NumInPallet / forkTime;
    
    // 计算总成本
    double SelectionTotalCost = (selectionTotalTravelTime * l->sel_rate) + 
                               (selectionPickHandlingTime * l->sel_rate) + 
                               (selectionStockerHandlingTime * l->stockerRate) + 
                               (fullPalletTotalTravelTime * l->fork_rate) + 
                               (fullPalletHandlingTime * l->fork_rate);
    
    Cost = SelectionTotalCost;
    
    return Cost;
}
```

## 3. 具体数值示例

### 3.1 输入数据

**产品数据**:
| 产品ID | 描述 | 销售量(每周箱数) | 箱体积(立方英尺) | 箱重量(磅) | 每箱数量 | 内包装数量 | 发货单位 |
|--------|------|-----------------|-----------------|------------|----------|------------|----------|
| P001   | 饮料A | 100             | 1.2             | 24         | 24       | 6          | 0 (每个) |
| P002   | 零食B | 50              | 0.8             | 15         | 36       | 12         | 0 (每个) |
| P003   | 清洁剂C | 25            | 2.0             | 32         | 12       | 4          | 0 (每个) |

**位置数据**:
| 位置ID | 货架类型 | 宽度(英寸) | 深度(英寸) | 高度(英寸) | 到拣选点距离(英尺) | 到叉车点距离(英尺) |
|--------|----------|------------|------------|------------|-------------------|-------------------|
| L001   | 标准货架 | 48         | 24         | 16         | 50                | 100               |
| L002   | 标准货架 | 48         | 24         | 16         | 75                | 120               |
| L003   | 流动货架 | 48         | 36         | 16         | 100               | 80                |

**劳动率**:
- 拣选工人: $0.50/分钟
- 存储工人: $0.45/分钟
- 叉车操作员: $0.60/分钟

**时间参数**:
- 拣选速度: 250英尺/分钟
- 叉车速度: 400英尺/分钟
- 拣选处理时间: 0.05分钟/箱
- 存储处理时间: 0.08分钟/箱
- 叉车处理时间: 2.0分钟/托盘

### 3.2 计算示例

**步骤1: 产品排序**
按销售量排序: P001(100) > P002(50) > P003(25)

**步骤2: 位置排序**
按到拣选点距离排序: L001(50) < L002(75) < L003(100)

**步骤3: 产品P001分配**

尝试位置L001:
```
拣选距离 = 50英尺
拣选时间 = 50 / 250 = 0.2分钟/趟
每箱拣选时间 = 0.2 / 3(每趟箱数) = 0.067分钟/箱
拣选处理时间 = 0.05分钟/箱
存储处理时间 = 0.08分钟/箱
总时间/箱 = 0.067 + 0.05 + 0.08 = 0.197分钟/箱
每周总时间 = 0.197 * 100 = 19.7分钟
拣选成本 = (0.067 + 0.05) * 100 * $0.50 = $5.85
存储成本 = 0.08 * 100 * $0.45 = $3.60
总成本 = $5.85 + $3.60 = $9.45
```

尝试位置L002:
```
拣选距离 = 75英尺
拣选时间 = 75 / 250 = 0.3分钟/趟
每箱拣选时间 = 0.3 / 3 = 0.1分钟/箱
总时间/箱 = 0.1 + 0.05 + 0.08 = 0.23分钟/箱
每周总时间 = 0.23 * 100 = 23分钟
拣选成本 = (0.1 + 0.05) * 100 * $0.50 = $7.50
存储成本 = 0.08 * 100 * $0.45 = $3.60
总成本 = $7.50 + $3.60 = $11.10
```

尝试位置L003:
```
拣选距离 = 100英尺
拣选时间 = 100 / 250 = 0.4分钟/趟
每箱拣选时间 = 0.4 / 3 = 0.133分钟/箱
总时间/箱 = 0.133 + 0.05 + 0 = 0.183分钟/箱 (流动货架不需要存储工人)
每周总时间 = 0.183 * 100 = 18.3分钟
拣选成本 = (0.133 + 0.05) * 100 * $0.50 = $9.15
存储成本 = $0 (流动货架)
总成本 = $9.15
```

最佳位置: L001 ($9.45)

**步骤4: 产品P002分配**
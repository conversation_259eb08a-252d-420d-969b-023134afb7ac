# Pass1 优化算法流程图

```mermaid
flowchart TD
    A[开始 Pass1 优化] --> B[初始化和数据准备]
    B --> C[从数据库收集产品信息]
    B --> D[收集货架类型信息]
    B --> E[收集设施中可用的货架位置信息]
    
    C & D & E --> F[建立与引擎的连接]
    F --> G[发送参数和配置]
    
    G --> H[发送货架类型数据]
    H --> I[发送货架类型使用规则]
    H --> J[发送货架配置数据]
    H --> K[发送产品组驱动参数]
    
    I & J & K --> L[发送产品数据]
    L --> M[引擎处理]
    
    M --> N[加载货架类型]
    N --> O[加载产品]
    O --> P[为每个产品分配货架类型]
    
    P --> Q{处理每个产品}
    Q --> R[调整产品值]
    R --> S[查找理想货架类型]
    S --> T[查找最佳可用货架类型]
    T --> U[排序可用货架类型]
    U --> V[更新可用空间]
    V --> Q
    
    Q -- 所有产品处理完毕 --> W[结果处理]
    W --> X[发送理想货架类型结果]
    W --> Y[发送最佳可用货架类型结果]
    W --> Z[发送货架使用摘要]
    
    X & Y & Z --> AA[服务器端处理结果]
    AA --> AB[解析引擎返回的结果]
    AB --> AC[将结果存储到数据库]
    AC --> AD[生成摘要报告]
    AD --> AE[结束 Pass1 优化]
```

# 产品适配检查流程

```mermaid
flowchart TD
    A[开始产品适配检查] --> B{产品是危险品?}
    B -- 是 --> C{货架支持危险品?}
    C -- 否 --> D[不适配]
    C -- 是 --> E{检查尺寸}
    
    B -- 否 --> F{货架是非危险品?}
    F -- 否 --> D
    F -- 是 --> E
    
    E --> G{检查宽度}
    G -- 不符合 --> H{尝试箱式处理?}
    H -- 是 --> I{箱式处理检查}
    H -- 否 --> D
    I -- 通过 --> J[适配但需调整处理方式]
    I -- 不通过 --> D
    
    G -- 符合 --> K{检查深度}
    K -- 不符合 --> H
    K -- 符合 --> L{检查高度}
    L -- 不符合 --> H
    L -- 符合 --> M{检查重量}
    M -- 不符合 --> H
    M -- 符合 --> N[完全适配]
    
    J --> O[返回适配结果]
    N --> O
    D --> O
    O --> P[结束产品适配检查]
```

# 货架类型排序流程

```mermaid
flowchart TD
    A[开始货架类型排序] --> B[收集所有可用货架类型]
    B --> C[按处理方法降序排序]
    C --> D[对相同处理方法的货架类型]
    D --> E[按高度差异升序排序]
    E --> F[更新排名]
    F --> G[结束货架类型排序]
```

# 空间使用计算流程

```mermaid
flowchart TD
    A[开始空间使用计算] --> B{货架类型是固定面还是线性面?}
    B -- 固定面 --> C[减少可用固定面数量]
    B -- 线性面 --> D[计算产品宽度]
    D --> E[减少可用线性面宽度]
    C & E --> F[更新货架类型使用统计]
    F --> G[检查是否需要更多此类货架]
    G --> H[结束空间使用计算]
```
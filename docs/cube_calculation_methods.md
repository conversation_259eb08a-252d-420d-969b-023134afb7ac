# 立方体计算方法详解

## 概述

在SLOT仓储优化系统中，立方体计算是核心功能之一，用于确定产品的空间需求和优化仓储布局。系统包含多种立方体计算方法，每种方法服务于不同的优化目标。

## 立方体类型

### 1. 基础立方体（Basic Cube）
**定义**：产品包装的物理体积
**计算公式**：
```
基础立方体 = (长 × 宽 × 高) ÷ 转换因子
```
**用途**：产品规格定义，空间占用基础计算

### 2. 扩展立方体（Extended Cube）
**定义**：考虑移动量的立方体计算
**计算公式**：
```
扩展立方体 = 基础立方体 × 移动量
```
**用途**：仓储布局优化，货架类型选择

### 3. 库存立方体（Inventory Cube）
**定义**：当前库存数量所占用的实际存储空间
**计算公式**：
```
库存立方体 = 基础立方体 × 当前库存数量
```
**用途**：当前空间占用分析，库存容量规划

## 移动量（Movement）详解

### 定义
移动量是指在特定时间周期内（通常是一周或一个月），某个产品从仓库中被取出或移动的数量，反映产品的流转频率和需求活跃度。

### 数据来源
1. **历史销售数据**：过去一段时间的实际出库记录
2. **需求预测**：基于历史趋势和业务预测的未来需求
3. **订单数据**：客户订单中的产品需求量
4. **数据库存储**：存储在产品包装表的Movement字段中

### 不同包装单位的移动量转换
```cpp
switch(aUnitOfIssue) {
    case 0: caseMovement = (aMovement / aCasePack);        // Each -> Case
            break;
    case 1: caseMovement = (aMovement * aInnerPack / aCasePack); // Inner -> Case
            break;
    case 2: caseMovement = (aMovement);                    // Case
            break;
    case 3: caseMovement = (aMovement * aNumInPallet);     // Pallet -> Case
            break;
}
```

## 实际数据例子

### 产品数据示例
| 产品 | 尺寸(英寸) | 移动量(箱/周) | 库存量(箱) | 基础立方体 | 扩展立方体 | 库存立方体 |
|------|------------|---------------|------------|------------|------------|------------|
| 可口可乐 | 24×16×8 | 500 | 150 | 1.78 ft³ | 890 ft³ | 267 ft³ |
| 薯片 | 18×12×6 | 150 | 80 | 0.75 ft³ | 112.5 ft³ | 60 ft³ |
| 调料 | 12×8×4 | 20 | 200 | 0.22 ft³ | 4.4 ft³ | 44 ft³ |

### 计算过程示例
**可口可乐产品：**
```
基础立方体 = (24 × 16 × 8) ÷ 1728 = 1.78 ft³
扩展立方体 = 1.78 × 500 = 890 ft³
库存立方体 = 1.78 × 150 = 267 ft³
```

## 货架选择中的应用

### Pass1：货架类型选择（基于扩展立方体）
- **高扩展立方体**：选择大型流动货架（便于频繁拣选）
- **中扩展立方体**：选择中型拣选货架（平衡效率和成本）
- **低扩展立方体**：选择小型储存货架（低成本存储）

### Pass4：具体位置分配（基于库存立方体）
- **容量检查**：验证选定货架是否有足够空间存放库存
- **空间分配**：根据库存立方体分配具体的存储空间
- **物理适配**：检查产品尺寸是否适合货架规格

## 优化决策逻辑

### 关键原则
1. **扩展立方体**：决定"应该放在哪种类型的货架上"（战略层面）
2. **库存立方体**：决定"需要多少空间来存放"（容量层面）
3. **物理尺寸**：决定"能否物理适配"（约束层面）

### 成本优化示例
**优化前（随机分配）：**
- 高移动量产品放在远距离位置 → 高拣选成本
- 低移动量产品占用便捷位置 → 空间浪费

**优化后（算法分配）：**
- 高移动量产品 → 流动货架 → 低拣选成本
- 低移动量产品 → 储存货架 → 低存储成本
- **总体成本降低8-15%**

## 扩展立方体计算类

### 新增功能
1. **多种计算类型**：基础、扩展、加权、动态、预测、优化
2. **批量计算**：支持多产品同时计算
3. **统计分析**：提供立方体统计信息
4. **优化算法**：空间利用率优化、位置分配优化
5. **单位转换**：支持多种体积单位转换

### 使用示例
```cpp
SLOTEnhancedCubeCalculator calculator;
CubeCalculationParams params;
params.conversionFactor = 1728.0;
params.includeMovement = true;

// 计算扩展立方体
double extendedCube = calculator.CalculateExtendedCube(productPack, movement, params);

// 综合计算
CubeCalculationResult result = calculator.CalculateComprehensiveCube(
    productPack, CUBE_TYPE_EXTENDED, params);
```

## 总结

立方体计算方法是仓储优化系统的核心，通过不同类型的立方体计算，系统能够：
- 准确评估产品的空间需求
- 优化货架类型选择
- 提高仓储空间利用率
- 降低操作成本
- 提升整体仓储效率

这种多维度的立方体计算方法确保了仓储布局决策的科学性和有效性。

#include "StdAfx.h"
#include "TestClass.h"
#include "DBSession.h"
#include "SlotProductPackDA.h"
#include "SLOTLocationInfo.h" 

// init
TestClass::TestClass(void)
{
}

TestClass::~TestClass(void)
{
}

void TestClass::run()
{
#if 0
    Library theDBVendorLibrary;
	ClassType theDBVendorClassType;
    theDBVendorLibrary = task.part.FindLibrary("Oracle",NIL,0,"OR",4);
	theDBVendorClassType = theDBVendorLibrary.FindClass("qqdb_OracleSession");
#endif
	DBSession sess;
	///sess = (DBSession)(theDBVendorClassType.InstanceAlloc());
	sess.Connect("@opti","exeslot","slotting");

	CListSLOTLocationInfoPtr infoArray (new CListSLOTLocationInfo);

	__int32 key = 0;
	__int32 rc = 0;
#if 0
	rc = (sql select externalkey into &key
		from dbproductpackf
		where dbfacilityid = 1069
		and wmsproductid = "1101060"
	on session sess);
	log->putline(key);
	log->putline(rc);
#endif
}

void TestClass::run2()
{
	CStringPtr t(new CString);
	/*
			ExternalSystemId|Facility|Section|Action|Batch|LineNumber|  6
			ProductPackDBID|Description|Weight|Movement|CaseWidth|CaseLength|CaseHeight| 13
			IsHazard|UnitOfIssue|IsPickToBelt|OptimizeBy|BalanceOnHand|NumberInPallet| 19
			RotateXAxis|RotateYAxis|RotateZAxis|IsAssignmentLocked|MaxStackNumber| 24
			EachLength|EachWidth|EachHeight|InnerLength|InnerWidth|InnerHeight| 30
			InnerPack|CasePack|WMSProductID|WMSProductDetailID|NumberOfHits| 35
			Status|IsActive|Trace|PreviousMovement|PreviousBOH| 40
			CommodityType|CrushFactor|ProductKey| 43
			ProductContainerDBID|Description|Width|Length|Height| 48
			IsWidthOverride|IsLengthOverride|IsHeightOverride|Ti|Hi| 53
	*/

	*t = ("999|1069|0|0|1014|1|-1|JHIRMACK HR SPRAY AERO EX HOLD  12 / 7 OZ|10.200000|8.620000|8.100000|10.200000|8.600000|0|2|0|0|31.050000|88|0|0|0|0|0|0.000000|0.000000|0.000000|0.000000|0.000000|0.000000|0|12|1101050|1|0.000000|1|1|0||0.000000|0.000000|||0|-1||40.000000|48.000000|0.000000|0|0|1|22|4|Action^Add|ExtendedCube^3.5444|ExtendedBOH^12.7674|IsStoreSupply^N|IsTobacco^N|IsRepack^N|IsBagged^N|IsAerosol^Y|IsLiquidToxic^N|CommodityMajor^11|CommodityMinor^1|CommodityGroup^Hair Spray|TargetSection^FF|");

	SLOTProductPackDA da;
	da.Database = "Optimize";

	CListCStringPtr a (new CListCString);
	a->AddTail(*t);
	// __int32 ec;			// COMMENTED - not required.
	//da.ProductInbound(a, ec);
}

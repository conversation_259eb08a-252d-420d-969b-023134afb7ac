#include "StdAfx.h"
#include "textparser.h"

//Init
TextParser::TextParser(void)
{
}

TextParser::~TextParser(void)
{
}

CString TextParser::BuildString(CListCStringPtr pStringList, CString pDelimiter, 
							bool pLeadingDelimiter, bool pTrailingDelimiter)
{
	CString newString;

	if (pLeadingDelimiter == true)
	{
		newString = pDelimiter;
	}

	POSITION posCSL = pStringList->GetHeadPosition();
	for (int i = 0; i < pStringList->GetCount(); i++)
	{
		newString += pStringList->GetNext(posCSL);
		newString += pDelimiter;
	}

	//Cut off last delimiter
	if ( (newString.GetLength() > 0) && (!pTrailingDelimiter) )
	{
		//CHECK1 - should this be reassigned to newString?, Forte code doesn't do that

		newString = newString.Left(newString.GetLength() - pDelimiter.GetLength());
		//newString.CutRange ( newString.ActualSize - pDelimiter.Actualsize, newString.ActualSize );
	}

	return newString;
}

CListCStringPtr TextParser::ParseToArray(CString pSource, CString pDelimiter, bool pTrim)
{
	__int32 DelimiterLength = pDelimiter.GetLength();
	CListCStringPtr Elements (new CListCString);
	CString ThisElement;
	CString Source = pSource;								// CHECK1
	__int32 OffsetTmp;

	OffsetTmp = Source.Find(pDelimiter);
	//Source.MoveToString ( source = pDelimiter );

	while (Source.GetLength() > 0)
	{
		//If there aren't anymore delimiters, just read the end and get out.
		if (OffsetTmp == -1)
		//if (Source.Offset == 0)
		{
			if (Source.Mid(0, DelimiterLength) == pDelimiter)
			//if Source.CopyRange ( 0, DelimiterLength ).IsEqual ( pDelimiter )
			{
				ThisElement = "";
			}
			else
			{
				if (pTrim)
				{
					Source.Trim();
				}
				Elements->AddTail(Source);
				break;
			}
		}
		else
		{
			ThisElement = Source.Mid(0, OffsetTmp);
			//ThisElement = Source.CutRange ( 0, Source.Offset );
		}

		if (pTrim)
		{
			ThisElement.Trim();
		}
		
		Source = Source.Mid( 0, DelimiterLength );			// CHECK1
		// Source.CutRange ( 0, DelimiterLength );

		Elements->AddTail(ThisElement);

		OffsetTmp = Source.Find(pDelimiter, OffsetTmp);
		// Source.MoveToString ( pDelimiter );
	}

	return Elements;
}

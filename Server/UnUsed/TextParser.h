#pragma once

class TextParser :
	public Object
{
public:
	TextParser(void);
	~TextParser(void);

	CString BuildString(CListCStringPtr pStringList, CString pDelimiter,
					bool pLeadingDelimiter = false, bool pTrailingDelimiter = false);	// CHECK1 - Noting to check!
	CListCStringPtr ParseToArray(CString pSource, CString pDelimiter, bool pTrim = true);
};

typedef auto_ptr<TextParser> TextParserPtr;
typedef CList<TextParser,TextParser&> CListTextParser;
typedef auto_ptr < CList<TextParser,TextParser&> > CListTextParserPtr;

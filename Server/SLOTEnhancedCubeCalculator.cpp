#include "StdAfx.h"
#include "SLOTEnhancedCubeCalculator.h"
#include "SrvcObjs.h"
#include "SLOTSectionDV.h"
#include "SLOTAisleDV.h"
#include "SLOTSideDV.h"
#include "SLOTBayDV.h"
#include "SLOTLevelDV.h"
#include "SLOTLocationDV.h"
#include "SLOTSolutionDV.h"
#include "SLOTProductPackDV.h"
#include "SLOTProductContainerDV.h"
#include <algorithm>
#include <cmath>

IMPLEMENT_DYNAMIC(SLOTEnhancedCubeCalculator, SLOTDataView)

// 构造函数
SLOTEnhancedCubeCalculator::SLOTEnhancedCubeCalculator(void)
{
    m_totalCalculatedCube = 0.0;
    m_calculationCount = 0;
    m_defaultParams = CubeCalculationParams();
}

SLOTEnhancedCubeCalculator::~SLOTEnhancedCubeCalculator(void)
{
    m_calculationHistory.clear();
}

SLOTEnhancedCubeCalculator::SLOTEnhancedCubeCalculator(const SLOTEnhancedCubeCalculator& src)
{
    SLOTEnhancedCubeCalculator();
    operator=(src);
}

const SLOTEnhancedCubeCalculator& SLOTEnhancedCubeCalculator::operator=(const SLOTEnhancedCubeCalculator& src)
{
    SLOTDataView::operator=(src);
    
    m_defaultParams = src.m_defaultParams;
    m_totalCalculatedCube = src.m_totalCalculatedCube;
    m_calculationCount = src.m_calculationCount;
    m_calculationHistory = src.m_calculationHistory;
    
    return *this;
}

// 基础立方体计算方法
double SLOTEnhancedCubeCalculator::CalculateBasicCube(double width, double height, double depth, double conversionFactor)
{
    // 验证输入参数
    if (!ValidateInputParameters(width, height, depth)) {
        return 0.0;
    }
    
    // 基础立方体计算：长 × 宽 × 高
    double rawCube = width * height * depth;
    
    // 应用转换因子
    double cube = ApplyConversionFactor(rawCube, conversionFactor);
    
    return cube;
}

double SLOTEnhancedCubeCalculator::CalculateBasicCube(const SLOTProductPack& productPack, const CubeCalculationParams& params)
{
    double width, height, depth;
    
    // 根据包装单位获取尺寸
    switch (productPack.GetUnitOfIssue()) {
        case 0: // Each
            width = productPack.GetEachDimension()->GetWidth();
            height = productPack.GetEachDimension()->GetHeight();
            depth = productPack.GetEachDimension()->GetDepth();
            break;
        case 1: // Inner
            width = productPack.GetInnerDimension()->GetWidth();
            height = productPack.GetInnerDimension()->GetHeight();
            depth = productPack.GetInnerDimension()->GetDepth();
            break;
        case 2: // Case
        default:
            width = productPack.GetDimension()->GetWidth();
            height = productPack.GetDimension()->GetHeight();
            depth = productPack.GetDimension()->GetDepth();
            break;
    }
    
    return CalculateBasicCube(width, height, depth, params.conversionFactor);
}

// 扩展立方体计算方法（兼容现有系统）
double SLOTEnhancedCubeCalculator::CalculateExtendedCube(const SLOTProductPack& productPack, double movement, const CubeCalculationParams& params)
{
    // 计算基础立方体
    double basicCube = CalculateBasicCube(productPack, params);
    
    if (!params.includeMovement) {
        return basicCube;
    }
    
    // 计算移动因子
    double movementFactor = CalculateMovementFactor(movement, 
                                                   productPack.GetUnitOfIssue(),
                                                   productPack.GetCasePack(),
                                                   productPack.GetInnerPack(),
                                                   productPack.GetNumInPallet());
    
    // 扩展立方体 = 基础立方体 × 移动因子
    double extendedCube = basicCube * movementFactor;
    
    return extendedCube;
}

// 加权立方体计算方法
double SLOTEnhancedCubeCalculator::CalculateWeightedCube(const SLOTProductPack& productPack, double weightFactor, double densityFactor, const CubeCalculationParams& params)
{
    // 计算基础立方体
    double basicCube = CalculateBasicCube(productPack, params);
    
    // 计算重量因子
    double adjustedWeightFactor = 1.0;
    if (params.includeWeight && productPack.GetWeight() > 0) {
        adjustedWeightFactor = weightFactor * (productPack.GetWeight() / 100.0); // 标准化重量
    }
    
    // 应用密度因子
    double density = CalculateDensity(productPack.GetWeight(), basicCube);
    double adjustedDensityFactor = densityFactor * (density / 10.0); // 标准化密度
    
    // 加权立方体 = 基础立方体 × 重量因子 × 密度因子
    double weightedCube = basicCube * adjustedWeightFactor * adjustedDensityFactor;
    
    return weightedCube;
}

// 动态立方体计算方法
double SLOTEnhancedCubeCalculator::CalculateDynamicCube(const SLOTProductPack& productPack, double timeHorizon, double seasonalFactor, const CubeCalculationParams& params)
{
    // 计算基础立方体
    double basicCube = CalculateBasicCube(productPack, params);
    
    // 应用季节性因子
    double adjustedSeasonalFactor = params.seasonalFactor * seasonalFactor;
    
    // 应用时间范围调整
    double timeAdjustment = 1.0 + (timeHorizon - 1.0) * 0.1; // 时间越长，调整越大
    
    // 动态立方体 = 基础立方体 × 季节性因子 × 时间调整
    double dynamicCube = basicCube * adjustedSeasonalFactor * timeAdjustment;
    
    return dynamicCube;
}

// 预测立方体计算方法
double SLOTEnhancedCubeCalculator::CalculatePredictiveCube(const SLOTProductPack& productPack, double growthRate, double forecastPeriod, const CubeCalculationParams& params)
{
    // 计算基础立方体
    double basicCube = CalculateBasicCube(productPack, params);
    
    // 应用增长因子
    double adjustedGrowthFactor = params.growthFactor * growthRate;
    
    // 计算预测因子：(1 + 增长率)^预测期间
    double forecastFactor = pow(1.0 + adjustedGrowthFactor, forecastPeriod);
    
    // 预测立方体 = 基础立方体 × 预测因子
    double predictiveCube = basicCube * forecastFactor;
    
    return predictiveCube;
}

// 优化立方体计算方法
double SLOTEnhancedCubeCalculator::CalculateOptimizedCube(const SLOTProductPack& productPack, const SLOTLocation& location, const CubeCalculationParams& params)
{
    // 计算基础立方体
    double basicCube = CalculateBasicCube(productPack, params);
    
    if (!params.optimizeSpace) {
        return basicCube;
    }
    
    // 计算空间利用率
    double spaceUtilization = CalculateSpaceUtilization(productPack, location);
    
    // 优化因子基于空间利用率
    double optimizationFactor = 1.0 / spaceUtilization; // 利用率越高，优化因子越小
    
    // 优化立方体 = 基础立方体 × 优化因子
    double optimizedCube = basicCube * optimizationFactor;
    
    return optimizedCube;
}

// 综合立方体计算方法
CubeCalculationResult SLOTEnhancedCubeCalculator::CalculateComprehensiveCube(const SLOTProductPack& productPack, CubeCalculationType type, const CubeCalculationParams& params)
{
    CubeCalculationResult result;
    
    // 计算基础立方体
    result.basicCube = CalculateBasicCube(productPack, params);
    
    // 根据类型计算相应的立方体
    switch (type) {
        case CUBE_TYPE_BASIC:
            result.calculationMethod = "Basic Cube Calculation";
            break;
            
        case CUBE_TYPE_EXTENDED:
            result.extendedCube = CalculateExtendedCube(productPack, productPack.GetMovement(), params);
            result.calculationMethod = "Extended Cube Calculation";
            break;
            
        case CUBE_TYPE_WEIGHTED:
            result.weightedCube = CalculateWeightedCube(productPack, params.weightFactor, params.densityFactor, params);
            result.calculationMethod = "Weighted Cube Calculation";
            break;
            
        case CUBE_TYPE_DYNAMIC:
            result.extendedCube = CalculateDynamicCube(productPack, 1.0, params.seasonalFactor, params);
            result.calculationMethod = "Dynamic Cube Calculation";
            break;
            
        case CUBE_TYPE_PREDICTIVE:
            result.extendedCube = CalculatePredictiveCube(productPack, params.growthFactor, 1.0, params);
            result.calculationMethod = "Predictive Cube Calculation";
            break;
            
        default:
            result.calculationMethod = "Basic Cube Calculation";
            break;
    }
    
    // 计算效率和利用率
    if (result.extendedCube > 0) {
        result.efficiency = result.basicCube / result.extendedCube;
        result.utilizationRate = result.extendedCube / (result.extendedCube + result.basicCube) * 100.0;
    }
    
    // 记录计算
    LogCalculation(result);
    
    return result;
}

// 立方体转换工具
double SLOTEnhancedCubeCalculator::ConvertCubeUnits(double cube, const CString& fromUnit, const CString& toUnit)
{
    // 转换因子映射
    std::map<CString, double> conversionFactors;
    conversionFactors["cubic_inches"] = 1.0;
    conversionFactors["cubic_feet"] = 1728.0;
    conversionFactors["cubic_meters"] = 61023.7;
    conversionFactors["cubic_centimeters"] = 0.0610237;
    
    // 获取转换因子
    double fromFactor = conversionFactors.count(fromUnit) ? conversionFactors[fromUnit] : 1.0;
    double toFactor = conversionFactors.count(toUnit) ? conversionFactors[toUnit] : 1.0;
    
    // 转换：先转换为立方英寸，再转换为目标单位
    return cube * fromFactor / toFactor;
}

double SLOTEnhancedCubeCalculator::CalculateDensity(double weight, double cube)
{
    if (cube <= 0) return 0.0;
    return weight / cube;
}

double SLOTEnhancedCubeCalculator::CalculateVolumeEfficiency(double usedCube, double totalCube)
{
    if (totalCube <= 0) return 0.0;
    return (usedCube / totalCube) * 100.0;
}

// 设置和获取参数
void SLOTEnhancedCubeCalculator::SetDefaultParams(const CubeCalculationParams& params)
{
    m_defaultParams = params;
}

CubeCalculationParams SLOTEnhancedCubeCalculator::GetDefaultParams() const
{
    return m_defaultParams;
}

void SLOTEnhancedCubeCalculator::SetConversionFactor(double factor)
{
    m_defaultParams.conversionFactor = factor;
}

double SLOTEnhancedCubeCalculator::GetConversionFactor() const
{
    return m_defaultParams.conversionFactor;
}

// 私有辅助方法实现
double SLOTEnhancedCubeCalculator::CalculateUnitCube(const SLOTProductPack& productPack, int unitOfIssue)
{
    double width, height, depth;

    switch (unitOfIssue) {
        case 0: // Each
            if (productPack.GetEachDimension()) {
                width = productPack.GetEachDimension()->GetWidth();
                height = productPack.GetEachDimension()->GetHeight();
                depth = productPack.GetEachDimension()->GetDepth();
            } else {
                return 0.0;
            }
            break;
        case 1: // Inner
            if (productPack.GetInnerDimension()) {
                width = productPack.GetInnerDimension()->GetWidth();
                height = productPack.GetInnerDimension()->GetHeight();
                depth = productPack.GetInnerDimension()->GetDepth();
            } else {
                return 0.0;
            }
            break;
        case 2: // Case
        default:
            if (productPack.GetDimension()) {
                width = productPack.GetDimension()->GetWidth();
                height = productPack.GetDimension()->GetHeight();
                depth = productPack.GetDimension()->GetDepth();
            } else {
                return 0.0;
            }
            break;
    }

    return width * height * depth;
}

double SLOTEnhancedCubeCalculator::ApplyConversionFactor(double rawCube, double conversionFactor)
{
    if (conversionFactor <= 0) {
        conversionFactor = m_defaultParams.conversionFactor;
    }
    return rawCube / conversionFactor;
}

double SLOTEnhancedCubeCalculator::CalculateMovementFactor(double movement, int unitOfIssue, int casePack, int innerPack, int numInPallet)
{
    double caseMovement = 0.0;

    // 确保包装数量不为零
    if (casePack == 0) casePack = 1;
    if (innerPack == 0) innerPack = 1;
    if (numInPallet == 0) numInPallet = 1;

    switch (unitOfIssue) {
        case 0: // Each
            caseMovement = movement / casePack;
            break;
        case 1: // Inner
            caseMovement = movement * innerPack / casePack;
            break;
        case 2: // Case
            caseMovement = movement;
            break;
        case 3: // Pallet
            caseMovement = movement * numInPallet;
            break;
        default:
            caseMovement = movement;
            break;
    }

    return caseMovement;
}

bool SLOTEnhancedCubeCalculator::ValidateInputParameters(double width, double height, double depth)
{
    return (width > 0 && height > 0 && depth > 0);
}

void SLOTEnhancedCubeCalculator::LogCalculation(const CubeCalculationResult& result)
{
    m_calculationHistory.push_back(result);
    m_totalCalculatedCube += result.basicCube + result.extendedCube + result.weightedCube + result.optimizedCube;
    m_calculationCount++;
}

double SLOTEnhancedCubeCalculator::CalculateSpaceUtilization(const SLOTProductPack& productPack, const SLOTLocation& location)
{
    // 获取产品尺寸
    double productWidth = productPack.GetDimension()->GetWidth();
    double productHeight = productPack.GetDimension()->GetHeight();
    double productDepth = productPack.GetDimension()->GetDepth();

    // 获取位置尺寸
    double locationWidth = location.GetWidth();
    double locationHeight = location.GetHeight();
    double locationDepth = location.GetDepth();

    // 计算产品体积
    double productVolume = productWidth * productHeight * productDepth;

    // 计算位置体积
    double locationVolume = locationWidth * locationHeight * locationDepth;

    // 计算利用率
    if (locationVolume <= 0) return 0.0;
    return productVolume / locationVolume;
}

// 统计计算辅助方法
double SLOTEnhancedCubeCalculator::CalculateStandardDeviation(const std::vector<double>& values, double mean)
{
    if (values.empty()) return 0.0;

    double sum = 0.0;
    for (const double& value : values) {
        sum += (value - mean) * (value - mean);
    }

    return sqrt(sum / values.size());
}

double SLOTEnhancedCubeCalculator::CalculateVariance(const std::vector<double>& values, double mean)
{
    if (values.empty()) return 0.0;

    double sum = 0.0;
    for (const double& value : values) {
        sum += (value - mean) * (value - mean);
    }

    return sum / values.size();
}

// 继承的方法
Object* SLOTEnhancedCubeCalculator::MapToObject()
{
    // 返回计算结果的数据对象
    typDoubleData* pDoubleData = new typDoubleData;
    pDoubleData->Value = m_totalCalculatedCube;
    return pDoubleData;
}

// 全局辅助函数实现
double CalculateQuickCube(double width, double height, double depth, double conversionFactor)
{
    if (width <= 0 || height <= 0 || depth <= 0) return 0.0;
    return (width * height * depth) / conversionFactor;
}

<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.10"
	Name="OptiServer"
	ProjectGUID="{198CC1F9-B144-4A83-9260-61032B64E8AC}"
	RootNamespace="OptiServer"
	Keyword="MFCDLLProj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="..\..\Obj.Debug"
			IntermediateDirectory="$(outdir)/Server"
			ConfigurationType="2"
			UseOfMFC="2"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\Common\core;..\..\Engine\Dispatch;..\..\Engine\Dispatch\Pass1;..\..\Engine\Dispatch\Pass3;..\..\Engine\Dispatch\Pass4"
				PreprocessorDefinitions="WIN32;_WINDOWS;_DEBUG;_USRDLL;Opti_EXPORTS;ONLY_OPTISERVER_INCLUDES"
				MinimalRebuild="TRUE"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				TreatWChar_tAsBuiltInType="TRUE"
				RuntimeTypeInfo="TRUE"
				UsePrecompiledHeader="3"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="4"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="$(outdir)\Libraries\Dispatch.lib $(outdir)\Libraries\RegKeyReader.lib $(outdir)\Libraries\slbtrieve.lib $(outdir)\Libraries\SsaGraphSession.lib"
				OutputFile="$(OutDir)/bin/OptiServer.dll"
				LinkIncremental="2"
				ModuleDefinitionFile=".\OptiServer.def"
				GenerateDebugInformation="TRUE"
				ProgramDatabaseFile="$(outdir)/Server/$(ProjectName).pdb"
				SubSystem="2"
				ImportLibrary="$(OutDir)/Libraries/OptiServer.lib"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="FALSE"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="$(IntDir)"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="..\..\Obj.Release"
			IntermediateDirectory="$(outdir)/Server"
			ConfigurationType="2"
			UseOfMFC="2"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="..\..\common\core;..\..\Engine\Dispatch;..\..\Engine\Dispatch\Pass1;..\..\Engine\Dispatch\Pass3;..\..\Engine\Dispatch\Pass4"
				PreprocessorDefinitions="WIN32;_WINDOWS;NDEBUG;_USRDLL;Opti_EXPORTS;ONLY_OPTISERVER_INCLUDES"
				RuntimeLibrary="2"
				TreatWChar_tAsBuiltInType="TRUE"
				UsePrecompiledHeader="3"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="3"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="$(outdir)\Libraries\Dispatch.lib $(outdir)\Libraries\RegKeyReader.lib $(outdir)\Libraries\slbtrieve.lib $(outdir)\Libraries\SsaGraphSession.lib"
				OutputFile="$(OutDir)/bin/OptiServer.dll"
				LinkIncremental="2"
				ModuleDefinitionFile=".\OptiServer.def"
				GenerateDebugInformation="TRUE"
				ProgramDatabaseFile="$(outdir)/Server/$(ProjectName).pdb"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				ImportLibrary="$(OutDir)/Libraries/OptiServer.lib"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="FALSE"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="$(IntDir)"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}">
			<File
				RelativePath="..\base.cpp">
			</File>
			<File
				RelativePath="..\DBDataSet.cpp">
			</File>
			<File
				RelativePath="..\DBDefinition.cpp">
			</File>
			<File
				RelativePath="..\DBPreparedStmt.cpp">
			</File>
			<File
				RelativePath="..\DBRouter.cpp">
			</File>
			<File
				RelativePath="..\DBSession.cpp">
			</File>
			<File
				RelativePath="..\DBStatementHandle.cpp">
			</File>
			<File
				RelativePath="..\DBVendor.cpp">
			</File>
			<File
				RelativePath="..\ErrorMgr.cpp">
			</File>
			<File
				RelativePath="..\HashTable.cpp">
			</File>
			<File
				RelativePath="..\MiscClasses.cpp">
			</File>
			<File
				RelativePath=".\OptiServer.cpp">
			</File>
			<File
				RelativePath=".\OptiServer.def">
			</File>
			<File
				RelativePath="..\RackRequirementClass.cpp">
			</File>
			<File
				RelativePath="..\RackUsageClass.cpp">
			</File>
			<File
				RelativePath="..\RandGenClass.cpp">
			</File>
			<File
				RelativePath="..\SLOTAdjective.cpp">
			</File>
			<File
				RelativePath="..\SLOTAisle.cpp">
			</File>
			<File
				RelativePath="..\SLOTAisleDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTAisleDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTAisleEntryExit.cpp">
			</File>
			<File
				RelativePath="..\SLOTAislePathDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTAisleProfile.cpp">
			</File>
			<File
				RelativePath="..\SLOTAisleProfileDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTAisleProfileDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTAisleSideProfile.cpp">
			</File>
			<File
				RelativePath="..\SLOTAisleSideProfileDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTAisleSideProfileDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTArea.cpp">
			</File>
			<File
				RelativePath="..\SLOTAreaDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTAreaDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTAssignmentQueue.cpp">
			</File>
			<File
				RelativePath="..\SLOTBay.cpp">
			</File>
			<File
				RelativePath="..\SLOTBayDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTBayDrawing.cpp">
			</File>
			<File
				RelativePath="..\SLOTBayDrawingDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTBayDrawingDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTBayDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTBayProfile.cpp">
			</File>
			<File
				RelativePath="..\SLOTBayProfileDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTBayProfileDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTBayRule.cpp">
			</File>
			<File
				RelativePath="..\SLOTBayRuleDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTBayRuleDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTChainData.cpp">
			</File>
			<File
				RelativePath="..\SLOTCoordinate.cpp">
			</File>
			<File
				RelativePath="..\SLOTCostAnalysis.cpp">
			</File>
			<File
				RelativePath="..\SLOTDataAdministrator.cpp">
			</File>
			<File
				RelativePath="..\SLOTDataMgr.cpp">
			</File>
			<File
				RelativePath="..\SLOTDataModelerMgr.cpp">
			</File>
			<File
				RelativePath="..\SLOTDataView.cpp">
			</File>
			<File
				RelativePath="..\SLOTDBObject.cpp">
			</File>
			<File
				RelativePath="..\SLOTDimension.cpp">
			</File>
			<File
				RelativePath="..\SLOTDVFactory.cpp">
			</File>
			<File
				RelativePath="..\SLOTEngineAisleDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTEngineBayDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTEngineLevelDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTEngineLocationDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTEngineP1ProdPackDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTEngineProdPackDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTEngineSectionDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTEngineSideDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTExportDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTExtendedCubeDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTFacility.cpp">
			</File>
			<File
				RelativePath="..\SLOTFacilityDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTFacilityDrawing.cpp">
			</File>
			<File
				RelativePath="..\SLOTFacilityDrawingDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTFacilityDrawingDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTFacilityDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTFacilityDwgDataDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTFacingInfo.cpp">
			</File>
			<File
				RelativePath="..\SLOTFacingInfoDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTFacingInfoDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTFloatDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTFuzzyVariable.cpp">
			</File>
			<File
				RelativePath="..\SLOTGroupBay.cpp">
			</File>
			<File
				RelativePath="..\SLOTGroupBayDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTGroupBayDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTGroupsToLevels.cpp">
			</File>
			<File
				RelativePath="..\SLOTGroupsToLevelsDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTGroupsToLevelsDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTGrpLevelDescriptionDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTHolder.cpp">
			</File>
			<File
				RelativePath="..\SLOTHotSpot.cpp">
			</File>
			<File
				RelativePath="..\SLOTHotspotDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTHotspotDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTImportDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTImportProducts.cpp">
			</File>
			<File
				RelativePath="..\SLOTIntegerClass.cpp">
			</File>
			<File
				RelativePath="..\SLOTIntegerDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTInterfaceLocationDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTInterfaceMoveDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTInterfaceSolutionDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTLevel.cpp">
			</File>
			<File
				RelativePath="..\SLOTLevelDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTLevelDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTLevelLaborProfile.cpp">
			</File>
			<File
				RelativePath="..\SLOTLevelLaborProfileDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTLevelLaborProfileDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTLevelLaborProfThin.cpp">
			</File>
			<File
				RelativePath="..\SLOTLevelLabProfThinDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTLevelProfile.cpp">
			</File>
			<File
				RelativePath="..\SLOTLevelProfileDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTLevelProfileDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTLocation.cpp">
			</File>
			<File
				RelativePath="..\SLOTLocationDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTLocationDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTLocationInfo.cpp">
			</File>
			<File
				RelativePath="..\SLOTLocationInfoDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTLocationProfile.cpp">
			</File>
			<File
				RelativePath="..\SLOTLocationProfileDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTLocationProfileDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTLocationThinDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTLocQueue.cpp">
			</File>
			<File
				RelativePath="..\SLOTMapElement.cpp">
			</File>
			<File
				RelativePath="..\SLOTMove.cpp">
			</File>
			<File
				RelativePath="..\SLOTMoveCostData.cpp">
			</File>
			<File
				RelativePath="..\SLOTMoveCostDataDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTMoveDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTMoveDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTObject.cpp">
			</File>
			<File
				RelativePath="..\SLOTP1RackData.cpp">
			</File>
			<File
				RelativePath="..\SLOTP1RackDataDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTP1RackReqDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTP1RackTypeUsage.cpp">
			</File>
			<File
				RelativePath="..\SLOTP1RackTypeUsageDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTP1ResultsThinDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTP3RackData.cpp">
			</File>
			<File
				RelativePath="..\SLOTP3RackDataDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1Manager.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1Message.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1MessageDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1MessageDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1RackUsage.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1RackUsageDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1Rejection.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1RejectionDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1RejectionDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1ResAvail.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1ResAvailDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1ResAvailDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1ResIdeal.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1ResIdealDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1ResIdealDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1Results.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1ResultsAvailThin.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1ResultsIdealThin.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1ResultsProfilesUsed.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1ResultsThin.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1Summary.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1SummaryDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass1SummaryDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass3Manager.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass3Summary.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass3SummaryDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass3SummaryDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass4Manager.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass4Message.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass4MessageDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass4MessageDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTPass5Manager.cpp">
			</File>
			<File
				RelativePath="..\SLOTPassManager.cpp">
			</File>
			<File
				RelativePath="..\SLOTPickPath.cpp">
			</File>
			<File
				RelativePath="..\SLOTPickPathDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTPickPathDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTProdLocThin.cpp">
			</File>
			<File
				RelativePath="..\SLOTProdLocThinDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTProdPKCountBySGDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTProdQueue.cpp">
			</File>
			<File
				RelativePath="..\SLOTProdSlotGroup.cpp">
			</File>
			<File
				RelativePath="..\SLOTProdSlotGroupDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTProdSlotGroupDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTProduct.cpp">
			</File>
			<File
				RelativePath="..\SLOTProductContainer.cpp">
			</File>
			<File
				RelativePath="..\SLOTProductContainerDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTProductContainerDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTProductDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTProductDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTProductPack.cpp">
			</File>
			<File
				RelativePath="..\SLOTProductPackDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTProductPackDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTQuery.cpp">
			</File>
			<File
				RelativePath="..\SLOTQueryAttr.cpp">
			</File>
			<File
				RelativePath="..\SLOTQueryAttrDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTQueryAttributeDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTQueryDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTQueryDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTRackType.cpp">
			</File>
			<File
				RelativePath="..\SLOTRackTypeGroup.cpp">
			</File>
			<File
				RelativePath="..\SLOTRackTypeLabor.cpp">
			</File>
			<File
				RelativePath="..\SLOTRackTypeUsage.cpp">
			</File>
			<File
				RelativePath="..\SLOTRegistryReaderClass.cpp">
			</File>
			<File
				RelativePath="..\SLOTRuleSet.cpp">
			</File>
			<File
				RelativePath="..\SLOTRulesSummaryDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTSaveAsDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTSection.cpp">
			</File>
			<File
				RelativePath="..\SLOTSectionDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTSectionDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTSessionMgr.cpp">
			</File>
			<File
				RelativePath="..\SLOTSide.cpp">
			</File>
			<File
				RelativePath="..\SLOTSideBayProfile.cpp">
			</File>
			<File
				RelativePath="..\SLOTSideBayProfileDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTSideBayProfileDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTSideDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTSideDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTSideProfile.cpp">
			</File>
			<File
				RelativePath="..\SLOTSideProfileDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTSideProfileDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTSlottingGroup.cpp">
			</File>
			<File
				RelativePath="..\SLOTSlottingGroupDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTSlottingGroupDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTSocketString.cpp">
			</File>
			<File
				RelativePath="..\SLOTSocketStringLarge.cpp">
			</File>
			<File
				RelativePath="..\SLOTSolution.cpp">
			</File>
			<File
				RelativePath="..\SLOTSolutionDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTSolutionDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTSolutionLocationDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTSolutionRun.cpp">
			</File>
			<File
				RelativePath="..\SLOTSolutionRunDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTSolutionThinDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTSQLStatement.cpp">
			</File>
			<File
				RelativePath="..\SLOTStringDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTTreeElement.cpp">
			</File>
			<File
				RelativePath="..\SLOTUDFDA.cpp">
			</File>
			<File
				RelativePath="..\SLOTUDFDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTUDFListDV.cpp">
			</File>
			<File
				RelativePath="..\SLOTUser.cpp">
			</File>
			<File
				RelativePath="..\SLOTUserDefinedField.cpp">
			</File>
			<File
				RelativePath="..\SSAConnectionServer.cpp">
			</File>
			<File
				RelativePath="..\SSALog.cpp">
			</File>
			<File
				RelativePath="..\SSAObject.cpp">
			</File>
			<File
				RelativePath="..\SSASocketAddress.cpp">
			</File>
			<File
				RelativePath=".\stdafx.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}">
			<File
				RelativePath="..\base.h">
			</File>
			<File
				RelativePath="..\baselineprocess.h">
			</File>
			<File
				RelativePath="..\DataStream.h">
			</File>
			<File
				RelativePath="..\DBDataSet.h">
			</File>
			<File
				RelativePath="..\DBDefinition.h">
			</File>
			<File
				RelativePath="..\DBPreparedStmt.h">
			</File>
			<File
				RelativePath="..\DBRouter.h">
			</File>
			<File
				RelativePath="..\DBSession.h">
			</File>
			<File
				RelativePath="..\DBStatementHandle.h">
			</File>
			<File
				RelativePath="..\DBVendor.h">
			</File>
			<File
				RelativePath="..\Dispatch.h">
			</File>
			<File
				RelativePath="..\ErrorMgr.h">
			</File>
			<File
				RelativePath="..\HashTable.h">
			</File>
			<File
				RelativePath="..\HashUtil.h">
			</File>
			<File
				RelativePath="..\MiscClasses.h">
			</File>
			<File
				RelativePath=".\OptiServer.h">
			</File>
			<File
				RelativePath="..\p1process.h">
			</File>
			<File
				RelativePath="..\p3process.h">
			</File>
			<File
				RelativePath="..\p4process.h">
			</File>
			<File
				RelativePath="..\RackRequirementClass.h">
			</File>
			<File
				RelativePath="..\RackUsageClass.h">
			</File>
			<File
				RelativePath="..\RandGenClass.h">
			</File>
			<File
				RelativePath="..\RegKeyReader.h">
			</File>
			<File
				RelativePath=".\Resource.h">
			</File>
			<File
				RelativePath="..\RTClassName.h">
			</File>
			<File
				RelativePath="..\SLOTAdjective.h">
			</File>
			<File
				RelativePath="..\SLOTAisle.h">
			</File>
			<File
				RelativePath="..\SLOTAisleDA.h">
			</File>
			<File
				RelativePath="..\SLOTAisleDV.h">
			</File>
			<File
				RelativePath="..\SLOTAisleEntryExit.h">
			</File>
			<File
				RelativePath="..\SLOTAislePathDA.h">
			</File>
			<File
				RelativePath="..\SLOTAisleProfile.h">
			</File>
			<File
				RelativePath="..\SLOTAisleProfileDA.h">
			</File>
			<File
				RelativePath="..\SLOTAisleProfileDV.h">
			</File>
			<File
				RelativePath="..\SLOTAisleSideProfile.h">
			</File>
			<File
				RelativePath="..\SLOTAisleSideProfileDA.h">
			</File>
			<File
				RelativePath="..\SLOTAisleSideProfileDV.h">
			</File>
			<File
				RelativePath="..\SLOTArea.h">
			</File>
			<File
				RelativePath="..\SLOTAreaDA.h">
			</File>
			<File
				RelativePath="..\SLOTAreaDV.h">
			</File>
			<File
				RelativePath="..\SLOTAssignmentQueue.h">
			</File>
			<File
				RelativePath="..\SLOTBay.h">
			</File>
			<File
				RelativePath="..\SLOTBayDA.h">
			</File>
			<File
				RelativePath="..\SLOTBayDrawing.h">
			</File>
			<File
				RelativePath="..\SLOTBayDrawingDA.h">
			</File>
			<File
				RelativePath="..\SLOTBayDrawingDV.h">
			</File>
			<File
				RelativePath="..\SLOTBayDV.h">
			</File>
			<File
				RelativePath="..\SLOTBayProfile.h">
			</File>
			<File
				RelativePath="..\SLOTBayProfileDA.h">
			</File>
			<File
				RelativePath="..\SLOTBayProfileDV.h">
			</File>
			<File
				RelativePath="..\SLOTBayRule.h">
			</File>
			<File
				RelativePath="..\SLOTBayRuleDA.h">
			</File>
			<File
				RelativePath="..\SLOTBayRuleDV.h">
			</File>
			<File
				RelativePath="..\SLOTChainData.h">
			</File>
			<File
				RelativePath="..\SLOTCoordinate.h">
			</File>
			<File
				RelativePath="..\SLOTCostAnalysis.h">
			</File>
			<File
				RelativePath="..\SLOTDataAdministrator.h">
			</File>
			<File
				RelativePath="..\SLOTDataMgr.h">
			</File>
			<File
				RelativePath="..\SLOTDataModelerMgr.h">
			</File>
			<File
				RelativePath="..\SLOTDataView.h">
			</File>
			<File
				RelativePath="..\SLOTDBObject.h">
			</File>
			<File
				RelativePath="..\SLOTDimension.h">
			</File>
			<File
				RelativePath="..\SLOTDVFactory.h">
			</File>
			<File
				RelativePath="..\SLOTEngineAisleDV.h">
			</File>
			<File
				RelativePath="..\SLOTEngineBayDV.h">
			</File>
			<File
				RelativePath="..\SLOTEngineLevelDV.h">
			</File>
			<File
				RelativePath="..\SLOTEngineLocationDV.h">
			</File>
			<File
				RelativePath="..\SLOTEngineP1ProdPackDV.h">
			</File>
			<File
				RelativePath="..\SLOTEngineProdPackDV.h">
			</File>
			<File
				RelativePath="..\SLOTEngineSectionDV.h">
			</File>
			<File
				RelativePath="..\SLOTEngineSideDV.h">
			</File>
			<File
				RelativePath="..\SLOTExportDA.h">
			</File>
			<File
				RelativePath="..\SLOTExtendedCubeDV.h">
			</File>
			<File
				RelativePath="..\SLOTFacility.h">
			</File>
			<File
				RelativePath="..\SLOTFacilityDA.h">
			</File>
			<File
				RelativePath="..\SLOTFacilityDrawing.h">
			</File>
			<File
				RelativePath="..\SLOTFacilityDrawingDA.h">
			</File>
			<File
				RelativePath="..\SLOTFacilityDrawingDV.h">
			</File>
			<File
				RelativePath="..\SLOTFacilityDV.h">
			</File>
			<File
				RelativePath="..\SLOTFacilityDwgDataDV.h">
			</File>
			<File
				RelativePath="..\SLOTFacingInfo.h">
			</File>
			<File
				RelativePath="..\SLOTFacingInfoDA.h">
			</File>
			<File
				RelativePath="..\SLOTFacingInfoDV.h">
			</File>
			<File
				RelativePath="..\SLOTFloatDV.h">
			</File>
			<File
				RelativePath="..\SLOTFuzzyVariable.h">
			</File>
			<File
				RelativePath="..\SLOTGroupBay.h">
			</File>
			<File
				RelativePath="..\SLOTGroupBayDA.h">
			</File>
			<File
				RelativePath="..\SLOTGroupBayDV.h">
			</File>
			<File
				RelativePath="..\SLOTGroupsToLevels.h">
			</File>
			<File
				RelativePath="..\SLOTGroupsToLevelsDA.h">
			</File>
			<File
				RelativePath="..\SLOTGroupsToLevelsDV.h">
			</File>
			<File
				RelativePath="..\SLOTGrpLevelDescriptionDV.h">
			</File>
			<File
				RelativePath="..\SLOTHolder.h">
			</File>
			<File
				RelativePath="..\SLOTHotSpot.h">
			</File>
			<File
				RelativePath="..\SLOTHotspotDA.h">
			</File>
			<File
				RelativePath="..\SLOTHotspotDV.h">
			</File>
			<File
				RelativePath="..\SLOTImportDA.h">
			</File>
			<File
				RelativePath="..\SLOTImportProducts.h">
			</File>
			<File
				RelativePath="..\SLOTIntegerClass.h">
			</File>
			<File
				RelativePath="..\SLOTIntegerDV.h">
			</File>
			<File
				RelativePath="..\SLOTInterfaceLocationDV.h">
			</File>
			<File
				RelativePath="..\SLOTInterfaceMoveDV.h">
			</File>
			<File
				RelativePath="..\SLOTInterfaceSolutionDV.h">
			</File>
			<File
				RelativePath="..\SLOTLevel.h">
			</File>
			<File
				RelativePath="..\SLOTLevelDA.h">
			</File>
			<File
				RelativePath="..\SLOTLevelDV.h">
			</File>
			<File
				RelativePath="..\SLOTLevelLaborProfile.h">
			</File>
			<File
				RelativePath="..\SLOTLevelLaborProfileDA.h">
			</File>
			<File
				RelativePath="..\SLOTLevelLaborProfileDV.h">
			</File>
			<File
				RelativePath="..\SLOTLevelLaborProfThin.h">
			</File>
			<File
				RelativePath="..\SLOTLevelLabProfThinDV.h">
			</File>
			<File
				RelativePath="..\SLOTLevelProfile.h">
			</File>
			<File
				RelativePath="..\SLOTLevelProfileDA.h">
			</File>
			<File
				RelativePath="..\SLOTLevelProfileDV.h">
			</File>
			<File
				RelativePath="..\SLOTLocation.h">
			</File>
			<File
				RelativePath="..\SLOTLocationDA.h">
			</File>
			<File
				RelativePath="..\SLOTLocationDV.h">
			</File>
			<File
				RelativePath="..\SLOTLocationInfo.h">
			</File>
			<File
				RelativePath="..\SLOTLocationInfoDV.h">
			</File>
			<File
				RelativePath="..\SLOTLocationProfile.h">
			</File>
			<File
				RelativePath="..\SLOTLocationProfileDA.h">
			</File>
			<File
				RelativePath="..\SLOTLocationProfileDV.h">
			</File>
			<File
				RelativePath="..\SLOTLocationThinDV.h">
			</File>
			<File
				RelativePath="..\SLOTLocQueue.h">
			</File>
			<File
				RelativePath="..\SLOTMapElement.h">
			</File>
			<File
				RelativePath="..\SLOTMove.h">
			</File>
			<File
				RelativePath="..\SLOTMoveCostData.h">
			</File>
			<File
				RelativePath="..\SLOTMoveCostDataDV.h">
			</File>
			<File
				RelativePath="..\SLOTMoveDA.h">
			</File>
			<File
				RelativePath="..\SLOTMoveDV.h">
			</File>
			<File
				RelativePath="..\SLOTObject.h">
			</File>
			<File
				RelativePath="..\SLOTP1RackData.h">
			</File>
			<File
				RelativePath="..\SLOTP1RackDataDV.h">
			</File>
			<File
				RelativePath="..\SLOTP1RackReqDV.h">
			</File>
			<File
				RelativePath="..\SLOTP1RackTypeUsage.h">
			</File>
			<File
				RelativePath="..\SLOTP1RackTypeUsageDV.h">
			</File>
			<File
				RelativePath="..\SLOTP1ResultsThinDV.h">
			</File>
			<File
				RelativePath="..\SLOTP3RackData.h">
			</File>
			<File
				RelativePath="..\SLOTP3RackDataDV.h">
			</File>
			<File
				RelativePath="..\SLOTPass1Manager.h">
			</File>
			<File
				RelativePath="..\SLOTPass1Message.h">
			</File>
			<File
				RelativePath="..\SLOTPass1MessageDA.h">
			</File>
			<File
				RelativePath="..\SLOTPass1MessageDV.h">
			</File>
			<File
				RelativePath="..\SLOTPass1RackUsage.h">
			</File>
			<File
				RelativePath="..\SLOTPass1RackUsageDV.h">
			</File>
			<File
				RelativePath="..\SLOTPass1Rejection.h">
			</File>
			<File
				RelativePath="..\SLOTPass1RejectionDA.h">
			</File>
			<File
				RelativePath="..\SLOTPass1RejectionDV.h">
			</File>
			<File
				RelativePath="..\SLOTPass1ResAvail.h">
			</File>
			<File
				RelativePath="..\SLOTPass1ResAvailDA.h">
			</File>
			<File
				RelativePath="..\SLOTPass1ResAvailDV.h">
			</File>
			<File
				RelativePath="..\SLOTPass1ResIdeal.h">
			</File>
			<File
				RelativePath="..\SLOTPass1ResIdealDA.h">
			</File>
			<File
				RelativePath="..\SLOTPass1ResIdealDV.h">
			</File>
			<File
				RelativePath="..\SLOTPass1Results.h">
			</File>
			<File
				RelativePath="..\SLOTPass1ResultsAvailThin.h">
			</File>
			<File
				RelativePath="..\SLOTPass1ResultsIdealThin.h">
			</File>
			<File
				RelativePath="..\SLOTPass1ResultsProfilesUsed.h">
			</File>
			<File
				RelativePath="..\SLOTPass1ResultsThin.h">
			</File>
			<File
				RelativePath="..\SLOTPass1Summary.h">
			</File>
			<File
				RelativePath="..\SLOTPass1SummaryDA.h">
			</File>
			<File
				RelativePath="..\SLOTPass1SummaryDV.h">
			</File>
			<File
				RelativePath="..\SLOTPass3Manager.h">
			</File>
			<File
				RelativePath="..\SLOTPass3Summary.h">
			</File>
			<File
				RelativePath="..\SLOTPass3SummaryDA.h">
			</File>
			<File
				RelativePath="..\SLOTPass3SummaryDV.h">
			</File>
			<File
				RelativePath="..\SLOTPass4Manager.h">
			</File>
			<File
				RelativePath="..\SLOTPass4Message.h">
			</File>
			<File
				RelativePath="..\SLOTPass4MessageDA.h">
			</File>
			<File
				RelativePath="..\SLOTPass4MessageDV.h">
			</File>
			<File
				RelativePath="..\SLOTPass5Manager.h">
			</File>
			<File
				RelativePath="..\SLOTPassManager.h">
			</File>
			<File
				RelativePath="..\SLOTPickPath.h">
			</File>
			<File
				RelativePath="..\SLOTPickPathDA.h">
			</File>
			<File
				RelativePath="..\SLOTPickPathDV.h">
			</File>
			<File
				RelativePath="..\SLOTProdLocThin.h">
			</File>
			<File
				RelativePath="..\SLOTProdLocThinDV.h">
			</File>
			<File
				RelativePath="..\SLOTProdPKCountBySGDV.h">
			</File>
			<File
				RelativePath="..\SLOTProdQueue.h">
			</File>
			<File
				RelativePath="..\SLOTProdSlotGroup.h">
			</File>
			<File
				RelativePath="..\SLOTProdSlotGroupDA.h">
			</File>
			<File
				RelativePath="..\SLOTProdSlotGroupDV.h">
			</File>
			<File
				RelativePath="..\SLOTProduct.h">
			</File>
			<File
				RelativePath="..\SLOTProductContainer.h">
			</File>
			<File
				RelativePath="..\SLOTProductContainerDA.h">
			</File>
			<File
				RelativePath="..\SLOTProductContainerDV.h">
			</File>
			<File
				RelativePath="..\SLOTProductDA.h">
			</File>
			<File
				RelativePath="..\SLOTProductDV.h">
			</File>
			<File
				RelativePath="..\SLOTProductPack.h">
			</File>
			<File
				RelativePath="..\SLOTProductPackDA.h">
			</File>
			<File
				RelativePath="..\SLOTProductPackDV.h">
			</File>
			<File
				RelativePath="..\SLOTQuery.h">
			</File>
			<File
				RelativePath="..\SLOTQueryAttr.h">
			</File>
			<File
				RelativePath="..\SLOTQueryAttrDV.h">
			</File>
			<File
				RelativePath="..\SLOTQueryAttributeDA.h">
			</File>
			<File
				RelativePath="..\SLOTQueryDA.h">
			</File>
			<File
				RelativePath="..\SLOTQueryDV.h">
			</File>
			<File
				RelativePath="..\SLOTRackType.h">
			</File>
			<File
				RelativePath="..\SLOTRackTypeGroup.h">
			</File>
			<File
				RelativePath="..\SLOTRackTypeLabor.h">
			</File>
			<File
				RelativePath="..\SLOTRackTypeUsage.h">
			</File>
			<File
				RelativePath="..\SLOTRegistryReaderClass.h">
			</File>
			<File
				RelativePath="..\SLOTRuleSet.h">
			</File>
			<File
				RelativePath="..\SLOTRulesSummaryDV.h">
			</File>
			<File
				RelativePath="..\SLOTSaveAsDA.h">
			</File>
			<File
				RelativePath="..\SLOTSection.h">
			</File>
			<File
				RelativePath="..\SLOTSectionDA.h">
			</File>
			<File
				RelativePath="..\SLOTSectionDV.h">
			</File>
			<File
				RelativePath="..\SLOTSessionMgr.h">
			</File>
			<File
				RelativePath="..\SLOTSide.h">
			</File>
			<File
				RelativePath="..\SLOTSideBayProfile.h">
			</File>
			<File
				RelativePath="..\SLOTSideBayProfileDA.h">
			</File>
			<File
				RelativePath="..\SLOTSideBayProfileDV.h">
			</File>
			<File
				RelativePath="..\SLOTSideDA.h">
			</File>
			<File
				RelativePath="..\SLOTSideDV.h">
			</File>
			<File
				RelativePath="..\SLOTSideProfile.h">
			</File>
			<File
				RelativePath="..\SLOTSideProfileDA.h">
			</File>
			<File
				RelativePath="..\SLOTSideProfileDV.h">
			</File>
			<File
				RelativePath="..\SLOTSlottingGroup.h">
			</File>
			<File
				RelativePath="..\SLOTSlottingGroupDA.h">
			</File>
			<File
				RelativePath="..\SLOTSlottingGroupDV.h">
			</File>
			<File
				RelativePath="..\SLOTSocketString.h">
			</File>
			<File
				RelativePath="..\SLOTSocketStringLarge.h">
			</File>
			<File
				RelativePath="..\SLOTSolution.h">
			</File>
			<File
				RelativePath="..\SLOTSolutionDA.h">
			</File>
			<File
				RelativePath="..\SLOTSolutionDV.h">
			</File>
			<File
				RelativePath="..\SLOTSolutionLocationDV.h">
			</File>
			<File
				RelativePath="..\SLOTSolutionRun.h">
			</File>
			<File
				RelativePath="..\SLOTSolutionRunDV.h">
			</File>
			<File
				RelativePath="..\SLOTSolutionThinDV.h">
			</File>
			<File
				RelativePath="..\SLOTSQLStatement.h">
			</File>
			<File
				RelativePath="..\SLOTStringDV.h">
			</File>
			<File
				RelativePath="..\SLOTTreeElement.h">
			</File>
			<File
				RelativePath="..\SLOTUDFDA.h">
			</File>
			<File
				RelativePath="..\SLOTUDFDV.h">
			</File>
			<File
				RelativePath="..\SLOTUDFListDV.h">
			</File>
			<File
				RelativePath="..\SLOTUser.h">
			</File>
			<File
				RelativePath="..\SLOTUserDefinedField.h">
			</File>
			<File
				RelativePath="..\SrvcObjs.h">
			</File>
			<File
				RelativePath="..\ssaBtree.h">
			</File>
			<File
				RelativePath="..\SSAConnectionServer.h">
			</File>
			<File
				RelativePath="..\..\Common\core\SSAEncrypt.h">
			</File>
			<File
				RelativePath="..\SSALog.h">
			</File>
			<File
				RelativePath="..\SSAObject.h">
			</File>
			<File
				RelativePath="..\SSASocketAddress.h">
			</File>
			<File
				RelativePath=".\stdafx.h">
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}">
			<File
				RelativePath=".\OptiServer.rc">
			</File>
			<File
				RelativePath=".\res\OptiServer.rc2">
			</File>
		</Filter>
		<File
			RelativePath=".\ReadMe.txt">
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>

// OptiServer.h : main header file for the OptiServer DLL
//

#pragma once

#ifndef __AFXWIN_H__
	#error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"		// main symbols


// COptiServerApp
// See OptiServer.cpp for the implementation of this class
//

class COptiServerApp : public CWinApp
{
public:
	COptiServerApp();

// Overrides
public:
	virtual BOOL InitInstance();

	DECLARE_MESSAGE_MAP()
};

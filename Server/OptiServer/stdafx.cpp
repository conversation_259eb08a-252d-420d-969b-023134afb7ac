// stdafx.cpp : source file that includes just the standard includes
// OptiServer.pch will be the pre-compiled header
// stdafx.obj will contain the pre-compiled type information

#include "stdafx.h"

//#pragma data_seg(".Share")

// MUST be the 1st SO entry
#include "..\SLOTSessionMgr.h"
SLOTSessionMgr *SessionMgrSO = new SLOTSessionMgr;

#include "..\DBRouter.h"
DBRouter DBRouterSO;

#include "..\SLOTCostAnalysis.h"
SLOTCostAnalysis CostAnalysisSO;

#include "..\SLOTDataModelerMgr.h"
SLOTDataModelerMgr DataModelerSO;

#include "..\SLOTDVFactory.h"
SLOTDVFactory SlotDVFactorySO;

#include "..\SLOTDataMgr.h"
SLOTDataMgr SLOTDataMgrSO;

#include "..\SLOTImportProducts.h"
SLOTImportProducts ImportProductsSO;

#if 0
#include "..\SLOTNewProdManager.h"
SLOTNewProdManager NewProdManagerSO;
#endif

#include "..\SLOTPass1Manager.h"
SLOTPass1Manager Pass1ManagerSO;


#include "..\SLOTPass3Manager.h"
SLOTPass3Manager Pass3ManagerSO;

#include "..\SLOTPass4Manager.h"
SLOTPass4Manager Pass4ManagerSO;

#include "..\SLOTPass5Manager.h"
SLOTPass5Manager Pass5ManagerSO;

///CHECK: shared segment not needed.
///#pragma data_seg()
///#pragma comment(linker, "/section:.Share,rws")

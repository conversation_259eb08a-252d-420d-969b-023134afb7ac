<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.10"
	Name="OptiServer"
	ProjectGUID="{198CC1F9-B144-4A83-9260-61032B64E8AC}"
	Keyword="MFCDLLProj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="\Program Files\EXE Technologies\Optimize\Bin"
			IntermediateDirectory="Debug"
			ConfigurationType="2"
			UseOfMFC="2"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="WIN32;_WINDOWS;_DEBUG;_USRDLL;Opti_EXPORTS;ONLY_OPTISERVER_INCLUDES"
				MinimalRebuild="TRUE"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				TreatWChar_tAsBuiltInType="TRUE"
				RuntimeTypeInfo="TRUE"
				UsePrecompiledHeader="3"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="4"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions="/FORCE:MULTIPLE"
				OutputFile="$(OutDir)/OptiServer.dll"
				LinkIncremental="2"
				ModuleDefinitionFile=".\OptiServer.def"
				GenerateDebugInformation="TRUE"
				SubSystem="2"
				ImportLibrary="$(OutDir)/OptiServer.lib"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="FALSE"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="$(IntDir)"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="Release"
			IntermediateDirectory="Release"
			ConfigurationType="2"
			UseOfMFC="2"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				PreprocessorDefinitions="WIN32;_WINDOWS;NDEBUG;_USRDLL"
				RuntimeLibrary="2"
				TreatWChar_tAsBuiltInType="TRUE"
				UsePrecompiledHeader="3"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="3"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(OutDir)/OptiServer.dll"
				LinkIncremental="1"
				ModuleDefinitionFile=".\OptiServer.def"
				GenerateDebugInformation="TRUE"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				ImportLibrary="$(OutDir)/OptiServer.lib"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="FALSE"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="$(IntDir)"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}">
			<File
				RelativePath="F:\base.cpp">
			</File>
			<File
				RelativePath="F:\DBDataSet.cpp">
			</File>
			<File
				RelativePath="F:\DBDefinition.cpp">
			</File>
			<File
				RelativePath="F:\DBPreparedStmt.cpp">
			</File>
			<File
				RelativePath="F:\DBRouter.cpp">
			</File>
			<File
				RelativePath="F:\DBSession.cpp">
			</File>
			<File
				RelativePath="F:\DBStatementHandle.cpp">
			</File>
			<File
				RelativePath="F:\DBVendor.cpp">
			</File>
			<File
				RelativePath="F:\ErrorMgr.cpp">
			</File>
			<File
				RelativePath="F:\EXEConnectionServer.cpp">
			</File>
			<File
				RelativePath="F:\EXELog.cpp">
			</File>
			<File
				RelativePath="F:\EXEObject.cpp">
			</File>
			<File
				RelativePath="F:\EXESocketAddress.cpp">
			</File>
			<File
				RelativePath="F:\HashTable.cpp">
			</File>
			<File
				RelativePath="F:\MiscClasses.cpp">
			</File>
			<File
				RelativePath=".\OptiServer.cpp">
			</File>
			<File
				RelativePath=".\OptiServer.def">
			</File>
			<File
				RelativePath="F:\RackRequirementClass.cpp">
			</File>
			<File
				RelativePath="F:\RackUsageClass.cpp">
			</File>
			<File
				RelativePath="F:\RandGenClass.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAdjective.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAisle.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAisleDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAisleDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAisleEntryExit.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAislePathDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAisleProfile.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAisleProfileDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAisleProfileDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAisleSideProfile.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAisleSideProfileDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAisleSideProfileDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTArea.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAreaDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAreaDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTAssignmentQueue.cpp">
			</File>
			<File
				RelativePath="F:\SLOTBay.cpp">
			</File>
			<File
				RelativePath="F:\SLOTBayDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTBayDrawing.cpp">
			</File>
			<File
				RelativePath="F:\SLOTBayDrawingDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTBayDrawingDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTBayDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTBayProfile.cpp">
			</File>
			<File
				RelativePath="F:\SLOTBayProfileDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTBayProfileDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTBayRule.cpp">
			</File>
			<File
				RelativePath="F:\SLOTBayRuleDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTBayRuleDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTChainData.cpp">
			</File>
			<File
				RelativePath="F:\SLOTCoordinate.cpp">
			</File>
			<File
				RelativePath="F:\SLOTCostAnalysis.cpp">
			</File>
			<File
				RelativePath="F:\SLOTDataAdministrator.cpp">
			</File>
			<File
				RelativePath="F:\SLOTDataMgr.cpp">
			</File>
			<File
				RelativePath="F:\SLOTDataModelerMgr.cpp">
			</File>
			<File
				RelativePath="F:\SLOTDataView.cpp">
			</File>
			<File
				RelativePath="F:\SLOTDBObject.cpp">
			</File>
			<File
				RelativePath="F:\SLOTDimension.cpp">
			</File>
			<File
				RelativePath="F:\SLOTDVFactory.cpp">
			</File>
			<File
				RelativePath="F:\SLOTEngineAisleDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTEngineBayDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTEngineLevelDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTEngineLocationDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTEngineP1ProdPackDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTEngineProdPackDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTEngineSectionDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTEngineSideDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTExportDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTExtendedCubeDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTFacility.cpp">
			</File>
			<File
				RelativePath="F:\SLOTFacilityDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTFacilityDrawing.cpp">
			</File>
			<File
				RelativePath="F:\SLOTFacilityDrawingDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTFacilityDrawingDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTFacilityDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTFacilityDwgDataDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTFacingInfo.cpp">
			</File>
			<File
				RelativePath="F:\SLOTFacingInfoDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTFacingInfoDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTFloatDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTFuzzyVariable.cpp">
			</File>
			<File
				RelativePath="F:\SLOTGroupBay.cpp">
			</File>
			<File
				RelativePath="F:\SLOTGroupBayDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTGroupBayDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTGroupsToLevels.cpp">
			</File>
			<File
				RelativePath="F:\SLOTGroupsToLevelsDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTGroupsToLevelsDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTGrpLevelDescriptionDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTHolder.cpp">
			</File>
			<File
				RelativePath="F:\SLOTHotSpot.cpp">
			</File>
			<File
				RelativePath="F:\SLOTHotspotDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTHotspotDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTImportDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTImportProducts.cpp">
			</File>
			<File
				RelativePath="F:\SLOTIntegerClass.cpp">
			</File>
			<File
				RelativePath="F:\SLOTIntegerDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTInterfaceLocationDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTInterfaceMoveDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTInterfaceSolutionDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLevel.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLevelDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLevelDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLevelLaborProfile.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLevelLaborProfileDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLevelLaborProfileDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLevelLaborProfThin.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLevelLabProfThinDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLevelProfile.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLevelProfileDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLevelProfileDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLocation.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLocationDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLocationDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLocationInfo.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLocationInfoDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLocationProfile.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLocationProfileDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLocationProfileDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLocationThinDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTLocQueue.cpp">
			</File>
			<File
				RelativePath="F:\SLOTMapElement.cpp">
			</File>
			<File
				RelativePath="F:\SLOTMove.cpp">
			</File>
			<File
				RelativePath="F:\SLOTMoveCostData.cpp">
			</File>
			<File
				RelativePath="F:\SLOTMoveCostDataDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTMoveDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTMoveDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTObject.cpp">
			</File>
			<File
				RelativePath="F:\SLOTP1RackData.cpp">
			</File>
			<File
				RelativePath="F:\SLOTP1RackDataDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTP1RackReqDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTP1RackTypeUsage.cpp">
			</File>
			<File
				RelativePath="F:\SLOTP1RackTypeUsageDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTP1ResultsThinDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTP3RackData.cpp">
			</File>
			<File
				RelativePath="F:\SLOTP3RackDataDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1Manager.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1Message.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1MessageDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1MessageDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1RackUsage.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1RackUsageDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1Rejection.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1RejectionDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1RejectionDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResAvail.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResAvailDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResAvailDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResIdeal.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResIdealDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResIdealDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1Results.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResultsAvailThin.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResultsIdealThin.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResultsProfilesUsed.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResultsThin.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1Summary.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1SummaryDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass1SummaryDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass3Manager.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass3Summary.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass3SummaryDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass3SummaryDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass4Manager.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass4Message.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass4MessageDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass4MessageDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPass5Manager.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPassManager.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPickPath.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPickPathDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTPickPathDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProdLocThin.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProdLocThinDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProdPKCountBySGDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProdQueue.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProdSlotGroup.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProdSlotGroupDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProdSlotGroupDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProduct.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProductContainer.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProductContainerDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProductContainerDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProductDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProductDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProductPack.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProductPackDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTProductPackDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTQuery.cpp">
			</File>
			<File
				RelativePath="F:\SLOTQueryAttr.cpp">
			</File>
			<File
				RelativePath="F:\SLOTQueryAttrDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTQueryAttributeDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTQueryDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTQueryDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTRackType.cpp">
			</File>
			<File
				RelativePath="F:\SLOTRackTypeGroup.cpp">
			</File>
			<File
				RelativePath="F:\SLOTRackTypeLabor.cpp">
			</File>
			<File
				RelativePath="F:\SLOTRackTypeUsage.cpp">
			</File>
			<File
				RelativePath="F:\SLOTRegistryReaderClass.cpp">
			</File>
			<File
				RelativePath="F:\SLOTRuleSet.cpp">
			</File>
			<File
				RelativePath="F:\SLOTRulesSummaryDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSaveAsDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSection.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSectionDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSectionDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSessionMgr.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSide.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSideBayProfile.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSideBayProfileDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSideBayProfileDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSideDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSideDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSideProfile.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSideProfileDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSideProfileDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSlottingGroup.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSlottingGroupDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSlottingGroupDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSocketString.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSocketStringLarge.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSolution.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSolutionDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSolutionDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSolutionLocationDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSolutionRun.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSolutionRunDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSolutionThinDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTSQLStatement.cpp">
			</File>
			<File
				RelativePath="F:\SLOTStringDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTTreeElement.cpp">
			</File>
			<File
				RelativePath="F:\SLOTUDFDA.cpp">
			</File>
			<File
				RelativePath="F:\SLOTUDFDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTUDFListDV.cpp">
			</File>
			<File
				RelativePath="F:\SLOTUser.cpp">
			</File>
			<File
				RelativePath="F:\SLOTUserDefinedField.cpp">
			</File>
			<File
				RelativePath=".\stdafx.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}">
			<File
				RelativePath="F:\base.h">
			</File>
			<File
				RelativePath="F:\baselineprocess.h">
			</File>
			<File
				RelativePath="F:\DataStream.h">
			</File>
			<File
				RelativePath="F:\DBDataSet.h">
			</File>
			<File
				RelativePath="F:\DBDefinition.h">
			</File>
			<File
				RelativePath="F:\DBPreparedStmt.h">
			</File>
			<File
				RelativePath="F:\DBRouter.h">
			</File>
			<File
				RelativePath="F:\DBSession.h">
			</File>
			<File
				RelativePath="F:\DBStatementHandle.h">
			</File>
			<File
				RelativePath="F:\DBVendor.h">
			</File>
			<File
				RelativePath="F:\Dispatch.h">
			</File>
			<File
				RelativePath="F:\ErrorMgr.h">
			</File>
			<File
				RelativePath="F:\exeBtree.h">
			</File>
			<File
				RelativePath="F:\EXEConnectionServer.h">
			</File>
			<File
				RelativePath="F:\EXELog.h">
			</File>
			<File
				RelativePath="F:\EXEObject.h">
			</File>
			<File
				RelativePath="F:\EXESocketAddress.h">
			</File>
			<File
				RelativePath="F:\HashTable.h">
			</File>
			<File
				RelativePath="F:\HashUtil.h">
			</File>
			<File
				RelativePath="F:\MiscClasses.h">
			</File>
			<File
				RelativePath=".\OptiServer.h">
			</File>
			<File
				RelativePath="F:\p1process.h">
			</File>
			<File
				RelativePath="F:\p3process.h">
			</File>
			<File
				RelativePath="F:\p4process.h">
			</File>
			<File
				RelativePath="F:\RackRequirementClass.h">
			</File>
			<File
				RelativePath="F:\RackUsageClass.h">
			</File>
			<File
				RelativePath="F:\RandGenClass.h">
			</File>
			<File
				RelativePath="F:\RegKeyReader.h">
			</File>
			<File
				RelativePath=".\Resource.h">
			</File>
			<File
				RelativePath="F:\RTClassName.h">
			</File>
			<File
				RelativePath="F:\SLOTAdjective.h">
			</File>
			<File
				RelativePath="F:\SLOTAisle.h">
			</File>
			<File
				RelativePath="F:\SLOTAisleDA.h">
			</File>
			<File
				RelativePath="F:\SLOTAisleDV.h">
			</File>
			<File
				RelativePath="F:\SLOTAisleEntryExit.h">
			</File>
			<File
				RelativePath="F:\SLOTAislePathDA.h">
			</File>
			<File
				RelativePath="F:\SLOTAisleProfile.h">
			</File>
			<File
				RelativePath="F:\SLOTAisleProfileDA.h">
			</File>
			<File
				RelativePath="F:\SLOTAisleProfileDV.h">
			</File>
			<File
				RelativePath="F:\SLOTAisleSideProfile.h">
			</File>
			<File
				RelativePath="F:\SLOTAisleSideProfileDA.h">
			</File>
			<File
				RelativePath="F:\SLOTAisleSideProfileDV.h">
			</File>
			<File
				RelativePath="F:\SLOTArea.h">
			</File>
			<File
				RelativePath="F:\SLOTAreaDA.h">
			</File>
			<File
				RelativePath="F:\SLOTAreaDV.h">
			</File>
			<File
				RelativePath="F:\SLOTAssignmentQueue.h">
			</File>
			<File
				RelativePath="F:\SLOTBay.h">
			</File>
			<File
				RelativePath="F:\SLOTBayDA.h">
			</File>
			<File
				RelativePath="F:\SLOTBayDrawing.h">
			</File>
			<File
				RelativePath="F:\SLOTBayDrawingDA.h">
			</File>
			<File
				RelativePath="F:\SLOTBayDrawingDV.h">
			</File>
			<File
				RelativePath="F:\SLOTBayDV.h">
			</File>
			<File
				RelativePath="F:\SLOTBayProfile.h">
			</File>
			<File
				RelativePath="F:\SLOTBayProfileDA.h">
			</File>
			<File
				RelativePath="F:\SLOTBayProfileDV.h">
			</File>
			<File
				RelativePath="F:\SLOTBayRule.h">
			</File>
			<File
				RelativePath="F:\SLOTBayRuleDA.h">
			</File>
			<File
				RelativePath="F:\SLOTBayRuleDV.h">
			</File>
			<File
				RelativePath="F:\SLOTChainData.h">
			</File>
			<File
				RelativePath="F:\SLOTCoordinate.h">
			</File>
			<File
				RelativePath="F:\SLOTCostAnalysis.h">
			</File>
			<File
				RelativePath="F:\SLOTDataAdministrator.h">
			</File>
			<File
				RelativePath="F:\SLOTDataMgr.h">
			</File>
			<File
				RelativePath="F:\SLOTDataModelerMgr.h">
			</File>
			<File
				RelativePath="F:\SLOTDataView.h">
			</File>
			<File
				RelativePath="F:\SLOTDBObject.h">
			</File>
			<File
				RelativePath="F:\SLOTDimension.h">
			</File>
			<File
				RelativePath="F:\SLOTDVFactory.h">
			</File>
			<File
				RelativePath="F:\SLOTEngineAisleDV.h">
			</File>
			<File
				RelativePath="F:\SLOTEngineBayDV.h">
			</File>
			<File
				RelativePath="F:\SLOTEngineLevelDV.h">
			</File>
			<File
				RelativePath="F:\SLOTEngineLocationDV.h">
			</File>
			<File
				RelativePath="F:\SLOTEngineP1ProdPackDV.h">
			</File>
			<File
				RelativePath="F:\SLOTEngineProdPackDV.h">
			</File>
			<File
				RelativePath="F:\SLOTEngineSectionDV.h">
			</File>
			<File
				RelativePath="F:\SLOTEngineSideDV.h">
			</File>
			<File
				RelativePath="F:\SLOTExportDA.h">
			</File>
			<File
				RelativePath="F:\SLOTExtendedCubeDV.h">
			</File>
			<File
				RelativePath="F:\SLOTFacility.h">
			</File>
			<File
				RelativePath="F:\SLOTFacilityDA.h">
			</File>
			<File
				RelativePath="F:\SLOTFacilityDrawing.h">
			</File>
			<File
				RelativePath="F:\SLOTFacilityDrawingDA.h">
			</File>
			<File
				RelativePath="F:\SLOTFacilityDrawingDV.h">
			</File>
			<File
				RelativePath="F:\SLOTFacilityDV.h">
			</File>
			<File
				RelativePath="F:\SLOTFacilityDwgDataDV.h">
			</File>
			<File
				RelativePath="F:\SLOTFacingInfo.h">
			</File>
			<File
				RelativePath="F:\SLOTFacingInfoDA.h">
			</File>
			<File
				RelativePath="F:\SLOTFacingInfoDV.h">
			</File>
			<File
				RelativePath="F:\SLOTFloatDV.h">
			</File>
			<File
				RelativePath="F:\SLOTFuzzyVariable.h">
			</File>
			<File
				RelativePath="F:\SLOTGroupBay.h">
			</File>
			<File
				RelativePath="F:\SLOTGroupBayDA.h">
			</File>
			<File
				RelativePath="F:\SLOTGroupBayDV.h">
			</File>
			<File
				RelativePath="F:\SLOTGroupsToLevels.h">
			</File>
			<File
				RelativePath="F:\SLOTGroupsToLevelsDA.h">
			</File>
			<File
				RelativePath="F:\SLOTGroupsToLevelsDV.h">
			</File>
			<File
				RelativePath="F:\SLOTGrpLevelDescriptionDV.h">
			</File>
			<File
				RelativePath="F:\SLOTHolder.h">
			</File>
			<File
				RelativePath="F:\SLOTHotSpot.h">
			</File>
			<File
				RelativePath="F:\SLOTHotspotDA.h">
			</File>
			<File
				RelativePath="F:\SLOTHotspotDV.h">
			</File>
			<File
				RelativePath="F:\SLOTImportDA.h">
			</File>
			<File
				RelativePath="F:\SLOTImportProducts.h">
			</File>
			<File
				RelativePath="F:\SLOTIntegerClass.h">
			</File>
			<File
				RelativePath="F:\SLOTIntegerDV.h">
			</File>
			<File
				RelativePath="F:\SLOTInterfaceLocationDV.h">
			</File>
			<File
				RelativePath="F:\SLOTInterfaceMoveDV.h">
			</File>
			<File
				RelativePath="F:\SLOTInterfaceSolutionDV.h">
			</File>
			<File
				RelativePath="F:\SLOTLevel.h">
			</File>
			<File
				RelativePath="F:\SLOTLevelDA.h">
			</File>
			<File
				RelativePath="F:\SLOTLevelDV.h">
			</File>
			<File
				RelativePath="F:\SLOTLevelLaborProfile.h">
			</File>
			<File
				RelativePath="F:\SLOTLevelLaborProfileDA.h">
			</File>
			<File
				RelativePath="F:\SLOTLevelLaborProfileDV.h">
			</File>
			<File
				RelativePath="F:\SLOTLevelLaborProfThin.h">
			</File>
			<File
				RelativePath="F:\SLOTLevelLabProfThinDV.h">
			</File>
			<File
				RelativePath="F:\SLOTLevelProfile.h">
			</File>
			<File
				RelativePath="F:\SLOTLevelProfileDA.h">
			</File>
			<File
				RelativePath="F:\SLOTLevelProfileDV.h">
			</File>
			<File
				RelativePath="F:\SLOTLocation.h">
			</File>
			<File
				RelativePath="F:\SLOTLocationDA.h">
			</File>
			<File
				RelativePath="F:\SLOTLocationDV.h">
			</File>
			<File
				RelativePath="F:\SLOTLocationInfo.h">
			</File>
			<File
				RelativePath="F:\SLOTLocationInfoDV.h">
			</File>
			<File
				RelativePath="F:\SLOTLocationProfile.h">
			</File>
			<File
				RelativePath="F:\SLOTLocationProfileDA.h">
			</File>
			<File
				RelativePath="F:\SLOTLocationProfileDV.h">
			</File>
			<File
				RelativePath="F:\SLOTLocationThinDV.h">
			</File>
			<File
				RelativePath="F:\SLOTLocQueue.h">
			</File>
			<File
				RelativePath="F:\SLOTMapElement.h">
			</File>
			<File
				RelativePath="F:\SLOTMove.h">
			</File>
			<File
				RelativePath="F:\SLOTMoveCostData.h">
			</File>
			<File
				RelativePath="F:\SLOTMoveCostDataDV.h">
			</File>
			<File
				RelativePath="F:\SLOTMoveDA.h">
			</File>
			<File
				RelativePath="F:\SLOTMoveDV.h">
			</File>
			<File
				RelativePath="F:\SLOTObject.h">
			</File>
			<File
				RelativePath="F:\SLOTP1RackData.h">
			</File>
			<File
				RelativePath="F:\SLOTP1RackDataDV.h">
			</File>
			<File
				RelativePath="F:\SLOTP1RackReqDV.h">
			</File>
			<File
				RelativePath="F:\SLOTP1RackTypeUsage.h">
			</File>
			<File
				RelativePath="F:\SLOTP1RackTypeUsageDV.h">
			</File>
			<File
				RelativePath="F:\SLOTP1ResultsThinDV.h">
			</File>
			<File
				RelativePath="F:\SLOTP3RackData.h">
			</File>
			<File
				RelativePath="F:\SLOTP3RackDataDV.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1Manager.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1Message.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1MessageDA.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1MessageDV.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1RackUsage.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1RackUsageDV.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1Rejection.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1RejectionDA.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1RejectionDV.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResAvail.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResAvailDA.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResAvailDV.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResIdeal.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResIdealDA.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResIdealDV.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1Results.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResultsAvailThin.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResultsIdealThin.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResultsProfilesUsed.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1ResultsThin.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1Summary.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1SummaryDA.h">
			</File>
			<File
				RelativePath="F:\SLOTPass1SummaryDV.h">
			</File>
			<File
				RelativePath="F:\SLOTPass3Manager.h">
			</File>
			<File
				RelativePath="F:\SLOTPass3Summary.h">
			</File>
			<File
				RelativePath="F:\SLOTPass3SummaryDA.h">
			</File>
			<File
				RelativePath="F:\SLOTPass3SummaryDV.h">
			</File>
			<File
				RelativePath="F:\SLOTPass4Manager.h">
			</File>
			<File
				RelativePath="F:\SLOTPass4Message.h">
			</File>
			<File
				RelativePath="F:\SLOTPass4MessageDA.h">
			</File>
			<File
				RelativePath="F:\SLOTPass4MessageDV.h">
			</File>
			<File
				RelativePath="F:\SLOTPass5Manager.h">
			</File>
			<File
				RelativePath="F:\SLOTPassManager.h">
			</File>
			<File
				RelativePath="F:\SLOTPickPath.h">
			</File>
			<File
				RelativePath="F:\SLOTPickPathDA.h">
			</File>
			<File
				RelativePath="F:\SLOTPickPathDV.h">
			</File>
			<File
				RelativePath="F:\SLOTProdLocThin.h">
			</File>
			<File
				RelativePath="F:\SLOTProdLocThinDV.h">
			</File>
			<File
				RelativePath="F:\SLOTProdPKCountBySGDV.h">
			</File>
			<File
				RelativePath="F:\SLOTProdQueue.h">
			</File>
			<File
				RelativePath="F:\SLOTProdSlotGroup.h">
			</File>
			<File
				RelativePath="F:\SLOTProdSlotGroupDA.h">
			</File>
			<File
				RelativePath="F:\SLOTProdSlotGroupDV.h">
			</File>
			<File
				RelativePath="F:\SLOTProduct.h">
			</File>
			<File
				RelativePath="F:\SLOTProductContainer.h">
			</File>
			<File
				RelativePath="F:\SLOTProductContainerDA.h">
			</File>
			<File
				RelativePath="F:\SLOTProductContainerDV.h">
			</File>
			<File
				RelativePath="F:\SLOTProductDA.h">
			</File>
			<File
				RelativePath="F:\SLOTProductDV.h">
			</File>
			<File
				RelativePath="F:\SLOTProductPack.h">
			</File>
			<File
				RelativePath="F:\SLOTProductPackDA.h">
			</File>
			<File
				RelativePath="F:\SLOTProductPackDV.h">
			</File>
			<File
				RelativePath="F:\SLOTQuery.h">
			</File>
			<File
				RelativePath="F:\SLOTQueryAttr.h">
			</File>
			<File
				RelativePath="F:\SLOTQueryAttrDV.h">
			</File>
			<File
				RelativePath="F:\SLOTQueryAttributeDA.h">
			</File>
			<File
				RelativePath="F:\SLOTQueryDA.h">
			</File>
			<File
				RelativePath="F:\SLOTQueryDV.h">
			</File>
			<File
				RelativePath="F:\SLOTRackType.h">
			</File>
			<File
				RelativePath="F:\SLOTRackTypeGroup.h">
			</File>
			<File
				RelativePath="F:\SLOTRackTypeLabor.h">
			</File>
			<File
				RelativePath="F:\SLOTRackTypeUsage.h">
			</File>
			<File
				RelativePath="F:\SLOTRegistryReaderClass.h">
			</File>
			<File
				RelativePath="F:\SLOTRuleSet.h">
			</File>
			<File
				RelativePath="F:\SLOTRulesSummaryDV.h">
			</File>
			<File
				RelativePath="F:\SLOTSaveAsDA.h">
			</File>
			<File
				RelativePath="F:\SLOTSection.h">
			</File>
			<File
				RelativePath="F:\SLOTSectionDA.h">
			</File>
			<File
				RelativePath="F:\SLOTSectionDV.h">
			</File>
			<File
				RelativePath="F:\SLOTSessionMgr.h">
			</File>
			<File
				RelativePath="F:\SLOTSide.h">
			</File>
			<File
				RelativePath="F:\SLOTSideBayProfile.h">
			</File>
			<File
				RelativePath="F:\SLOTSideBayProfileDA.h">
			</File>
			<File
				RelativePath="F:\SLOTSideBayProfileDV.h">
			</File>
			<File
				RelativePath="F:\SLOTSideDA.h">
			</File>
			<File
				RelativePath="F:\SLOTSideDV.h">
			</File>
			<File
				RelativePath="F:\SLOTSideProfile.h">
			</File>
			<File
				RelativePath="F:\SLOTSideProfileDA.h">
			</File>
			<File
				RelativePath="F:\SLOTSideProfileDV.h">
			</File>
			<File
				RelativePath="F:\SLOTSlottingGroup.h">
			</File>
			<File
				RelativePath="F:\SLOTSlottingGroupDA.h">
			</File>
			<File
				RelativePath="F:\SLOTSlottingGroupDV.h">
			</File>
			<File
				RelativePath="F:\SLOTSocketString.h">
			</File>
			<File
				RelativePath="F:\SLOTSocketStringLarge.h">
			</File>
			<File
				RelativePath="F:\SLOTSolution.h">
			</File>
			<File
				RelativePath="F:\SLOTSolutionDA.h">
			</File>
			<File
				RelativePath="F:\SLOTSolutionDV.h">
			</File>
			<File
				RelativePath="F:\SLOTSolutionLocationDV.h">
			</File>
			<File
				RelativePath="F:\SLOTSolutionRun.h">
			</File>
			<File
				RelativePath="F:\SLOTSolutionRunDV.h">
			</File>
			<File
				RelativePath="F:\SLOTSolutionThinDV.h">
			</File>
			<File
				RelativePath="F:\SLOTSQLStatement.h">
			</File>
			<File
				RelativePath="F:\SLOTStringDV.h">
			</File>
			<File
				RelativePath="F:\SLOTTreeElement.h">
			</File>
			<File
				RelativePath="F:\SLOTUDFDA.h">
			</File>
			<File
				RelativePath="F:\SLOTUDFDV.h">
			</File>
			<File
				RelativePath="F:\SLOTUDFListDV.h">
			</File>
			<File
				RelativePath="F:\SLOTUser.h">
			</File>
			<File
				RelativePath="F:\SLOTUserDefinedField.h">
			</File>
			<File
				RelativePath="F:\SrvcObjs.h">
			</File>
			<File
				RelativePath=".\stdafx.h">
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}">
			<File
				RelativePath=".\OptiServer.rc">
			</File>
			<File
				RelativePath=".\res\OptiServer.rc2">
			</File>
		</Filter>
		<File
			RelativePath="..\..\Dispatch_DLLProject.NET\Debug\Dispatch.lib">
		</File>
		<File
			RelativePath=".\ReadMe.txt">
		</File>
		<File
			RelativePath="..\..\..\Program Files\EXE Technologies\Optimize\Bin\RegKeyReader.lib">
		</File>
		<File
			RelativePath="..\..\..\Program Files\EXE Technologies\Optimize\Bin\slbtrieve.lib">
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
